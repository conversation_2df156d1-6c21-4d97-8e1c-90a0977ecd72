#!/bin/bash

# Auto.js云群控系统部署检查脚本
# 用于检查部署环境和服务状态

echo "🔍 Auto.js云群控系统部署检查"
echo "=" $(printf "%0.s=" {1..40})

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 检查函数
check_command() {
    if command -v $1 &> /dev/null; then
        echo -e "${GREEN}✅ $1 已安装${NC}"
        return 0
    else
        echo -e "${RED}❌ $1 未安装${NC}"
        return 1
    fi
}

check_port() {
    if netstat -tlnp 2>/dev/null | grep -q ":$1 "; then
        echo -e "${GREEN}✅ 端口 $1 正在监听${NC}"
        return 0
    else
        echo -e "${RED}❌ 端口 $1 未监听${NC}"
        return 1
    fi
}

check_service() {
    if systemctl is-active --quiet $1; then
        echo -e "${GREEN}✅ $1 服务运行中${NC}"
        return 0
    else
        echo -e "${RED}❌ $1 服务未运行${NC}"
        return 1
    fi
}

check_file() {
    if [ -f "$1" ]; then
        echo -e "${GREEN}✅ 文件存在: $1${NC}"
        return 0
    else
        echo -e "${RED}❌ 文件不存在: $1${NC}"
        return 1
    fi
}

check_directory() {
    if [ -d "$1" ]; then
        echo -e "${GREEN}✅ 目录存在: $1${NC}"
        return 0
    else
        echo -e "${RED}❌ 目录不存在: $1${NC}"
        return 1
    fi
}

# 系统环境检查
echo -e "\n${BLUE}📋 系统环境检查${NC}"
echo "----------------------------------------"

# 检查操作系统
if [[ "$OSTYPE" == "linux-gnu"* ]]; then
    echo -e "${GREEN}✅ 操作系统: Linux${NC}"
    OS_TYPE="linux"
elif [[ "$OSTYPE" == "msys" ]] || [[ "$OSTYPE" == "cygwin" ]]; then
    echo -e "${GREEN}✅ 操作系统: Windows${NC}"
    OS_TYPE="windows"
else
    echo -e "${YELLOW}⚠️  操作系统: $OSTYPE (可能需要手动配置)${NC}"
    OS_TYPE="other"
fi

# 检查必要的命令
echo -e "\n${BLUE}🔧 必要工具检查${NC}"
echo "----------------------------------------"
check_command "node"
check_command "npm"
check_command "pm2"

if [ "$OS_TYPE" = "linux" ]; then
    check_command "nginx"
    check_command "mysql"
fi

# 检查Node.js版本
if command -v node &> /dev/null; then
    NODE_VERSION=$(node --version)
    echo -e "${BLUE}📦 Node.js版本: $NODE_VERSION${NC}"
    
    # 检查版本是否满足要求 (v16+)
    NODE_MAJOR=$(echo $NODE_VERSION | cut -d'.' -f1 | sed 's/v//')
    if [ "$NODE_MAJOR" -ge 16 ]; then
        echo -e "${GREEN}✅ Node.js版本满足要求 (>=16)${NC}"
    else
        echo -e "${RED}❌ Node.js版本过低，需要v16或更高版本${NC}"
    fi
fi

# 检查项目文件
echo -e "\n${BLUE}📁 项目文件检查${NC}"
echo "----------------------------------------"
check_file "server-main.js"
check_file "package.json"
check_file "ecosystem.config.js"
check_directory "server"
check_directory "web"
check_directory "jb"
check_directory "xy-jb"
check_directory "scripts"

# 检查配置文件
echo -e "\n${BLUE}⚙️  配置文件检查${NC}"
echo "----------------------------------------"
check_file "server/config/database.js"
check_file "web/vue.config.js"
check_file "scripts/双向.js"

if [ -f "config/environment.js" ]; then
    check_file "config/environment.js"
else
    echo -e "${YELLOW}⚠️  建议创建统一配置文件: config/environment.js${NC}"
fi

# 检查端口占用
echo -e "\n${BLUE}🔌 端口检查${NC}"
echo "----------------------------------------"
check_port "3002"  # Node.js服务端口
check_port "80"    # HTTP端口
check_port "3306"  # MySQL端口

# 检查服务状态
if [ "$OS_TYPE" = "linux" ]; then
    echo -e "\n${BLUE}🚀 服务状态检查${NC}"
    echo "----------------------------------------"
    check_service "nginx"
    check_service "mysql"
fi

# 检查PM2进程
echo -e "\n${BLUE}📊 PM2进程检查${NC}"
echo "----------------------------------------"
if command -v pm2 &> /dev/null; then
    PM2_LIST=$(pm2 list 2>/dev/null)
    if echo "$PM2_LIST" | grep -q "autojs-control"; then
        echo -e "${GREEN}✅ PM2进程 autojs-control 运行中${NC}"
        pm2 show autojs-control 2>/dev/null | grep -E "(status|cpu|memory)"
    else
        echo -e "${RED}❌ PM2进程 autojs-control 未运行${NC}"
    fi
else
    echo -e "${RED}❌ PM2 未安装${NC}"
fi

# 检查数据库连接
echo -e "\n${BLUE}🗄️  数据库连接检查${NC}"
echo "----------------------------------------"
if [ -f "server/config/database.js" ]; then
    # 从配置文件中提取数据库信息
    DB_HOST=$(grep -o "host: '[^']*'" server/config/database.js | cut -d"'" -f2)
    DB_USER=$(grep -o "user: '[^']*'" server/config/database.js | cut -d"'" -f2)
    DB_NAME=$(grep -o "database: '[^']*'" server/config/database.js | cut -d"'" -f2)
    
    echo -e "${BLUE}数据库主机: $DB_HOST${NC}"
    echo -e "${BLUE}数据库用户: $DB_USER${NC}"
    echo -e "${BLUE}数据库名称: $DB_NAME${NC}"
    
    # 尝试连接数据库（需要用户输入密码）
    if command -v mysql &> /dev/null; then
        echo -e "${YELLOW}💡 可以手动测试数据库连接: mysql -h $DB_HOST -u $DB_USER -p $DB_NAME${NC}"
    fi
fi

# 检查防火墙状态
if [ "$OS_TYPE" = "linux" ]; then
    echo -e "\n${BLUE}🔥 防火墙状态检查${NC}"
    echo "----------------------------------------"
    if command -v ufw &> /dev/null; then
        UFW_STATUS=$(ufw status 2>/dev/null)
        if echo "$UFW_STATUS" | grep -q "Status: active"; then
            echo -e "${GREEN}✅ UFW防火墙已启用${NC}"
            echo "$UFW_STATUS" | grep -E "(80|443|3002|3306)"
        else
            echo -e "${YELLOW}⚠️  UFW防火墙未启用${NC}"
        fi
    fi
fi

# 检查磁盘空间
echo -e "\n${BLUE}💾 磁盘空间检查${NC}"
echo "----------------------------------------"
if command -v df &> /dev/null; then
    DISK_USAGE=$(df -h / 2>/dev/null | tail -1 | awk '{print $5}' | sed 's/%//')
    if [ "$DISK_USAGE" -lt 80 ]; then
        echo -e "${GREEN}✅ 磁盘空间充足 (已使用: $DISK_USAGE%)${NC}"
    else
        echo -e "${YELLOW}⚠️  磁盘空间不足 (已使用: $DISK_USAGE%)${NC}"
    fi
fi

# 检查内存使用
echo -e "\n${BLUE}🧠 内存使用检查${NC}"
echo "----------------------------------------"
if command -v free &> /dev/null; then
    MEMORY_INFO=$(free -h)
    echo "$MEMORY_INFO"
    
    MEMORY_USAGE=$(free | grep Mem | awk '{printf("%.0f", $3/$2 * 100.0)}')
    if [ "$MEMORY_USAGE" -lt 80 ]; then
        echo -e "${GREEN}✅ 内存使用正常 (已使用: $MEMORY_USAGE%)${NC}"
    else
        echo -e "${YELLOW}⚠️  内存使用较高 (已使用: $MEMORY_USAGE%)${NC}"
    fi
fi

# 网络连接检查
echo -e "\n${BLUE}🌐 网络连接检查${NC}"
echo "----------------------------------------"
if command -v curl &> /dev/null; then
    # 检查外网连接
    if curl -s --connect-timeout 5 http://www.baidu.com > /dev/null; then
        echo -e "${GREEN}✅ 外网连接正常${NC}"
    else
        echo -e "${RED}❌ 外网连接失败${NC}"
    fi
    
    # 检查本地API
    if curl -s --connect-timeout 5 http://localhost:3002/api/devices > /dev/null; then
        echo -e "${GREEN}✅ 本地API服务正常${NC}"
    else
        echo -e "${RED}❌ 本地API服务无响应${NC}"
    fi
fi

# 生成报告总结
echo -e "\n${BLUE}📋 检查总结${NC}"
echo "=" $(printf "%0.s=" {1..40})

# 统计检查结果
TOTAL_CHECKS=0
PASSED_CHECKS=0

# 这里可以添加更详细的统计逻辑

echo -e "${BLUE}💡 建议操作:${NC}"
echo "1. 确保所有必要的服务都在运行"
echo "2. 检查防火墙配置，开放必要端口"
echo "3. 验证数据库连接和权限"
echo "4. 测试前端页面访问"
echo "5. 测试设备连接功能"

echo -e "\n${BLUE}📚 相关命令:${NC}"
echo "查看PM2状态: pm2 status"
echo "查看PM2日志: pm2 logs autojs-control"
echo "重启服务: pm2 restart autojs-control"
echo "查看Nginx状态: sudo systemctl status nginx"
echo "查看Nginx日志: sudo tail -f /var/log/nginx/error.log"

echo -e "\n${GREEN}🎉 检查完成！${NC}"
