/**
 * 服务器核心配置模块 - 完整拆分版本
 * 包含数据库连接、服务初始化、CORS配置、文件上传配置等核心功能
 * 对应原始文件第1-338行的完整内容
 */

const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const cors = require('cors');
const path = require('path');
const multer = require('multer');
const fs = require('fs');

// 核心配置设置函数
async function setupServerCore() {
  console.log('🔧 设置服务器核心模块...');

  // 尝试引入数据库连接池
  let pool = null;
  try {
    const { pool: dbPool, initDatabase } = require('../config/database');
    pool = dbPool;
    console.log('数据库连接池已加载');

    // 初始化数据库
    initDatabase().then(() => {
      console.log('数据库初始化完成');
    }).catch(error => {
      console.error('数据库初始化失败:', error);
    });
  } catch (error) {
    console.log('数据库不可用（将使用内存模式）:', error.message);
  }

  // 尝试引入小红书日志服务（如果数据库可用）
  let xiaohongshuLogService = null;
  try {
    console.log('🔍 正在加载小红书日志服务...');
    xiaohongshuLogService = require('../services/xiaohongshuLogService');
    console.log('✅ 小红书日志服务已加载');

    // 检查和修复数据库表结构
    setTimeout(async () => {
      if (pool) {
        try {
          console.log('🔍 检查数据库表结构...');

          // 检查function_type枚举值
          const [columns] = await pool.execute(`
            SELECT COLUMN_TYPE
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_SCHEMA = 'autojs_control'
              AND TABLE_NAME = 'xiaohongshu_execution_logs'
              AND COLUMN_NAME = 'function_type'
          `);

          if (columns.length > 0) {
            const columnType = columns[0].COLUMN_TYPE;
            console.log('📋 当前function_type枚举值:', columnType);

            if (!columnType.includes('videoPublish')) {
              console.log('🔧 数据库表不支持videoPublish，正在修复...');

              // 更新枚举值，添加videoPublish支持
              await pool.execute(`
                ALTER TABLE xiaohongshu_execution_logs
                MODIFY COLUMN function_type ENUM('profile', 'groupChat', 'searchGroupChat', 'groupMessage', 'articleComment', 'uidMessage', 'uidFileMessage', 'videoPublish') NOT NULL
              `);

              console.log('✅ 数据库表已更新，现在支持videoPublish');

              // 再次检查
              const [newColumns] = await pool.execute(`
                SELECT COLUMN_TYPE
                FROM INFORMATION_SCHEMA.COLUMNS
                WHERE TABLE_SCHEMA = 'autojs_control'
                  AND TABLE_NAME = 'xiaohongshu_execution_logs'
                  AND COLUMN_NAME = 'function_type'
              `);
              console.log('📋 更新后的function_type枚举值:', newColumns[0].COLUMN_TYPE);
            } else if (!columnType.includes('uidMessage')) {
              console.log('🔧 数据库表不支持uidMessage，正在修复...');

              // 更新枚举值
              await pool.execute(`
                ALTER TABLE xiaohongshu_execution_logs
                MODIFY COLUMN function_type ENUM('profile', 'groupChat', 'searchGroupChat', 'groupMessage', 'articleComment', 'uidMessage', 'uidFileMessage', 'videoPublish') NOT NULL
              `);

              console.log('✅ 数据库表已更新，现在支持uidMessage和videoPublish');

              // 再次检查
              const [newColumns] = await pool.execute(`
                SELECT COLUMN_TYPE
                FROM INFORMATION_SCHEMA.COLUMNS
                WHERE TABLE_SCHEMA = 'autojs_control'
                  AND TABLE_NAME = 'xiaohongshu_execution_logs'
                  AND COLUMN_NAME = 'function_type'
              `);
              console.log('📋 更新后的function_type枚举值:', newColumns[0].COLUMN_TYPE);
            } else {
              console.log('✅ 数据库表已支持所有功能类型');
            }
          } else {
            console.error('❌ 找不到xiaohongshu_execution_logs表的function_type列');
          }

          // 检查是否有uidMessage记录
          const [records] = await pool.execute(`
            SELECT COUNT(*) as count
            FROM xiaohongshu_execution_logs
            WHERE function_type = 'uidMessage'
          `);
          console.log(`📊 数据库中uidMessage记录数量: ${records[0].count}`);

        } catch (dbError) {
          console.error('❌ 数据库检查失败:', dbError.message);
        }
      }
    }, 2000); // 2秒后执行检查

  } catch (error) {
    console.error('❌ 小红书日志服务加载失败:', error.message);
    console.error('❌ 错误详情:', error.stack);
    console.log('💡 这可能是因为：');
    console.log('   1. MySQL服务未启动');
    console.log('   2. 数据库连接配置错误');
    console.log('   3. 数据库表不存在');
  }

  // 尝试引入闲鱼日志服务（如果数据库可用）
  let xianyuLogService = null;
  try {
    xianyuLogService = require('../services/xianyuLogService');
    console.log('闲鱼日志服务已加载');
  } catch (error) {
    console.log('闲鱼日志服务不可用（可能是数据库未配置）:', error.message);
  }

  // 尝试引入闲鱼私聊服务（如果数据库可用）
  let xianyuChatService = null;
  try {
    xianyuChatService = require('../services/xianyuChatService');
    console.log('闲鱼私聊服务已加载');
  } catch (error) {
    console.log('闲鱼私聊服务不可用（可能是数据库未配置）:', error.message);
  }

  const app = express();
  const server = http.createServer(app);
  const io = socketIo(server, {
    cors: {
      origin: function (origin, callback) {
        // 允许没有origin的请求
        if (!origin) return callback(null, true);

        // 允许localhost和本地IP地址的所有端口
        const allowedOrigins = [
          /^http:\/\/localhost:\d+$/,
          /^http:\/\/127\.0\.0\.1:\d+$/,
          /^http:\/\/192\.168\.\d+\.\d+:\d+$/,
          /^http:\/\/10\.\d+\.\d+\.\d+:\d+$/,
          /^http:\/\/172\.(1[6-9]|2\d|3[01])\.\d+\.\d+:\d+$/
        ];

        const isAllowed = allowedOrigins.some(pattern => pattern.test(origin));
        callback(null, isAllowed);
      },
      methods: ["GET", "POST"],
      credentials: true
    }
  });

  // 将WebSocket实例设置到Express app中，供路由使用
  app.set('io', io);
  console.log('✅ WebSocket实例已设置到Express app');

  // 中间件 - 配置CORS以支持跨域访问
  const corsOptions = {
    origin: function (origin, callback) {
      console.log('🔍 [CORS] 检查Origin:', origin);

      // 允许没有origin的请求（如移动应用、Postman等）
      if (!origin) {
        console.log('✅ [CORS] 无Origin头，允许访问');
        return callback(null, true);
      }

      // 允许localhost和本地IP地址的所有端口
      const allowedOrigins = [
        /^http:\/\/localhost:\d+$/,
        /^http:\/\/127\.0\.0\.1:\d+$/,
        /^http:\/\/192\.168\.\d+\.\d+:\d+$/,
        /^http:\/\/10\.\d+\.\d+\.\d+:\d+$/,
        /^http:\/\/172\.(1[6-9]|2\d|3[01])\.\d+\.\d+:\d+$/
      ];

      const isAllowed = allowedOrigins.some(pattern => pattern.test(origin));

      if (isAllowed) {
        console.log('✅ [CORS] Origin允许:', origin);
        callback(null, true);
      } else {
        console.log('❌ [CORS] Origin被阻止:', origin);
        // 对于开发环境，我们可以更宽松一些
        console.log('⚠️ [CORS] 开发模式：允许所有Origin');
        callback(null, true);
      }
    },
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With', 'Accept', 'Origin', 'Access-Control-Request-Method', 'Access-Control-Request-Headers'],
    optionsSuccessStatus: 200,
    preflightContinue: false
  };

  app.use(cors(corsOptions));

  // 添加请求日志中间件
  app.use((req, res, next) => {
    // 使用节流日志，避免频繁输出请求信息
    const requestLogKey = `request_${req.method}_${req.url.split('?')[0]}`;
    const timestamp = new Date().toISOString();
    throttledLog(requestLogKey, `📥 [${timestamp}] ${req.method} ${req.url}`);
    throttledLog(`${requestLogKey}_details`, `📥 [请求详情] Origin: ${req.headers.origin || 'none'}, Host: ${req.headers.host || 'none'}, IP: ${req.ip || req.connection.remoteAddress || 'unknown'}`);
    next();
  });

  // 添加强制CORS中间件，确保所有响应都包含CORS头
  app.use((req, res, next) => {
    const origin = req.headers.origin;

    // 强制设置CORS头，允许所有来源
    if (origin) {
      res.header('Access-Control-Allow-Origin', origin);
      res.header('Access-Control-Allow-Credentials', 'true');
    } else {
      res.header('Access-Control-Allow-Origin', '*');
      res.header('Access-Control-Allow-Credentials', 'false');
    }

    res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS, PATCH, HEAD');
    res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With, Accept, Origin, Access-Control-Request-Method, Access-Control-Request-Headers');
    res.header('Access-Control-Max-Age', '86400');

    // 使用节流日志，避免频繁输出CORS信息
    const corsLogKey = `cors_${req.method}_${req.url.split('?')[0]}`;
    throttledLog(corsLogKey, `🔧 [强制CORS] ${req.method} ${req.url} - Origin: ${origin || 'none'}`);

    // 如果是OPTIONS请求，直接返回200
    if (req.method === 'OPTIONS') {
      throttledLog(`${corsLogKey}_options`, '🔍 [强制CORS] 处理OPTIONS预检请求: ' + req.url);
      res.status(200).end();
      return;
    }
    next();
  });

  // 处理预检请求（增强版）
  app.options('*', (req, res) => {
    console.log('🔍 [CORS] 收到OPTIONS预检请求');
    console.log('🔍 [CORS] URL:', req.url);
    console.log('🔍 [CORS] Origin:', req.headers.origin);
    console.log('🔍 [CORS] Method:', req.headers['access-control-request-method']);
    console.log('🔍 [CORS] Headers:', req.headers['access-control-request-headers']);
    console.log('🔍 [CORS] User-Agent:', req.headers['user-agent']);

    const origin = req.headers.origin;

    // 对于开发环境，我们允许所有Origin
    if (origin) {
      res.header('Access-Control-Allow-Origin', origin);
      console.log('✅ [CORS] 设置Origin为:', origin);
    } else {
      res.header('Access-Control-Allow-Origin', '*');
      console.log('ℹ️ [CORS] 无Origin头，使用通配符');
    }

    // 设置允许的方法
    const requestedMethod = req.headers['access-control-request-method'];
    if (requestedMethod) {
      res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS, PATCH');
      console.log('✅ [CORS] 允许方法:', requestedMethod);
    } else {
      res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS, PATCH');
    }

    // 设置允许的头部
    const requestedHeaders = req.headers['access-control-request-headers'];
    if (requestedHeaders) {
      res.header('Access-Control-Allow-Headers', requestedHeaders);
      console.log('✅ [CORS] 允许头部:', requestedHeaders);
    } else {
      res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With, Accept, Origin, Access-Control-Request-Method, Access-Control-Request-Headers');
    }

    res.header('Access-Control-Allow-Credentials', 'true');
    res.header('Access-Control-Max-Age', '86400'); // 24小时缓存

    console.log('✅ [CORS] 预检请求处理完成，返回200');
    res.status(200).end();
  });

  app.use(express.json({ limit: '2gb' })); // 支持大文件上传
  app.use(express.urlencoded({ extended: true, limit: '2gb' })); // 支持大文件上传

  // 配置静态文件服务
  app.use('/uploads', express.static(path.join(__dirname, '../uploads')));
  app.use('/thumbnails', express.static(path.join(__dirname, '../uploads/thumbnails')));

  // 配置multer用于文件上传
  const storage = multer.diskStorage({
    destination: function (req, file, cb) {
      const uploadDir = path.join(__dirname, '../uploads/uids');
      if (!fs.existsSync(uploadDir)) {
        fs.mkdirSync(uploadDir, { recursive: true });
      }
      cb(null, uploadDir);
    },
    filename: function (req, file, cb) {
      const timestamp = Date.now();
      const originalName = Buffer.from(file.originalname, 'latin1').toString('utf8');
      cb(null, `${timestamp}_${originalName}`);
    }
  });

  const upload = multer({
    storage: storage,
    fileFilter: function (req, file, cb) {
      const allowedTypes = ['.txt', '.csv'];
      const ext = path.extname(file.originalname).toLowerCase();
      if (allowedTypes.includes(ext)) {
        cb(null, true);
      } else {
        cb(new Error('只支持 .txt 和 .csv 格式的文件'));
      }
    },
    limits: {
      fileSize: 10 * 1024 * 1024 // 10MB
    }
  });

  // 配置视频文件上传（来自原始文件第6532-6580行）
  const videoStorage = multer.diskStorage({
    destination: function (req, file, cb) {
      const uploadDir = path.join(__dirname, 'uploads/videos');
      if (!fs.existsSync(uploadDir)) {
        fs.mkdirSync(uploadDir, { recursive: true });
      }
      cb(null, uploadDir);
    },
    filename: function (req, file, cb) {
      const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
      const ext = path.extname(file.originalname);
      cb(null, 'video-' + uniqueSuffix + ext);
    }
  });

  // 创建专门用于视频上传的multer实例（来自原始文件第6548-6580行）
  const videoUpload = multer({
    storage: videoStorage,
    fileFilter: function (req, file, cb) {
      console.log('视频文件过滤检查:', {
        fieldname: file.fieldname,
        originalname: file.originalname,
        mimetype: file.mimetype,
        size: file.size
      });

      // 只接受videos字段
      if (file.fieldname !== 'videos') {
        console.error('错误的字段名:', file.fieldname, '期望: videos');
        return cb(new Error(`错误的字段名: ${file.fieldname}，期望: videos`));
      }

      // 支持常见的视频格式
      const allowedTypes = ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.mkv', '.m4v', '.3gp'];
      const ext = path.extname(file.originalname).toLowerCase();
      if (allowedTypes.includes(ext)) {
        console.log('视频文件验证通过:', file.originalname);
        cb(null, true);
      } else {
        console.error('不支持的视频格式:', ext, '支持的格式:', allowedTypes);
        cb(new Error(`不支持的视频格式: ${ext}，支持的格式: ${allowedTypes.join(', ')}`));
      }
    },
    limits: {
      fileSize: 2 * 1024 * 1024 * 1024, // 2GB
      files: 1000 // 最多1000个文件
    }
  });

  // 内存存储（测试用）
  const devices = new Map();
  const webClients = new Map();
  const logs = [];
  const pendingCommands = new Map(); // 存储待执行的命令
  const deviceCommands = {}; // 存储设备命令队列
  const recentlyDisconnectedDevices = new Map(); // 存储最近断开连接的设备

  // 日志频率限制机制
  const logThrottleMap = new Map(); // 存储日志的最后输出时间
  const LOG_THROTTLE_INTERVAL = 5 * 60 * 1000; // 5分钟间隔

  // 节流日志函数
  function throttledLog(key, message) {
    const now = Date.now();
    const lastLogTime = logThrottleMap.get(key);

    if (!lastLogTime || (now - lastLogTime) >= LOG_THROTTLE_INTERVAL) {
      console.log(message);
      logThrottleMap.set(key, now);
      return true;
    }
    return false;
  }

  // JWT认证中间件
  const jwt = require('jsonwebtoken');
  const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';

  const authenticateToken = (req, res, next) => {
    try {
      const authHeader = req.headers['authorization'];
      const token = authHeader && authHeader.split(' ')[1];

      // 使用节流日志，避免频繁输出相同的认证信息
      const logKey = `auth_${req.method}_${req.path}`;
      throttledLog(logKey, `认证检查: ${req.method} ${req.path}`);
      throttledLog(`${logKey}_header`, `Authorization头: ${authHeader}`);
      throttledLog(`${logKey}_token`, `提取的token: ${token ? token.substring(0, 20) + '...' : 'null'}`);

      if (!token) {
        return res.status(401).json({
          success: false,
          message: '访问令牌缺失'
        });
      }

      // 验证JWT token
      const decoded = jwt.verify(token, JWT_SECRET);
      req.user = {
        id: decoded.userId,
        username: decoded.username,
        email: decoded.email,
        role: decoded.role
      };

      throttledLog(`${logKey}_success`, `认证成功: 用户${decoded.username}`);
      next();
    } catch (error) {
      console.error('JWT验证失败:', error.message);
      return res.status(401).json({
        success: false,
        message: '访问令牌无效或已过期'
      });
    }
  };

  // 清理小红书任务的函数
  async function cleanupXiaohongshuTasksForDevice(deviceId) {
    console.log(`清理设备 ${deviceId} 相关的小红书任务`);

    if (pool && xiaohongshuLogService) {
      try {
        // 查询该设备正在执行的小红书任务
        const [runningLogs] = await pool.execute(`
          SELECT task_id, function_type, started_at
          FROM xiaohongshu_execution_logs
          WHERE device_id = ? AND execution_status IN ('running', 'pending')
        `, [deviceId]);

        if (runningLogs.length > 0) {
          console.log(`发现设备 ${deviceId} 有 ${runningLogs.length} 个正在执行或等待的任务`);

          // 更新所有正在执行的任务为设备断开连接失败状态
          const [updateResult] = await pool.execute(`
            UPDATE xiaohongshu_execution_logs
            SET execution_status = 'failed',
                progress_percentage = 0,
                error_message = '设备断开连接',
                execution_result = '执行失败',
                completed_at = NOW(),
                execution_duration = TIMESTAMPDIFF(SECOND, started_at, NOW())
            WHERE device_id = ? AND execution_status IN ('running', 'pending')
          `, [deviceId]);

          console.log(`已更新 ${updateResult.affectedRows} 个小红书任务状态为失败（设备断开连接）`);

          // 通知前端更新执行状态
          for (const log of runningLogs) {
            console.log(`通知前端任务 ${log.task_id} 执行失败（设备断开连接）`);
            io.emit('xiaohongshu_execution_update', {
              taskId: log.task_id,
              deviceId: deviceId,
              status: 'failed',
              progress: 0,
              stage: 'error',
              message: '设备断开连接，任务执行失败',
              timestamp: new Date()
            });
          }

          // 发送设备状态重置通知给前端
          io.emit('device_execution_reset', {
            deviceId: deviceId,
            type: 'xiaohongshu',
            affectedTasks: runningLogs.length,
            reason: 'device_disconnected',
            timestamp: new Date()
          });
        } else {
          console.log(`设备 ${deviceId} 没有正在执行的小红书任务`);
        }
      } catch (error) {
        console.error(`清理设备 ${deviceId} 小红书任务失败:`, error);
      }
    }
  }

  // 清理闲鱼任务的函数
  async function cleanupXianyuTasksForDevice(deviceId) {
    console.log(`清理设备 ${deviceId} 相关的闲鱼任务`);

    if (pool && xianyuLogService) {
      try {
        // 查询该设备正在执行的闲鱼任务
        const runningLogs = await xianyuLogService.getExecutionLogs(1, 50, {
          deviceId: deviceId,
          executionStatus: 'running'
        });

        if (runningLogs.data && runningLogs.data.length > 0) {
          console.log(`发现设备 ${deviceId} 有 ${runningLogs.data.length} 个正在执行的闲鱼任务`);

          // 更新所有正在执行的任务为设备断开连接失败状态
          for (const log of runningLogs.data) {
            await xianyuLogService.updateExecutionStatus(
              log.task_id,
              'failed',
              0,
              null,
              '设备断开连接'
            );

            // 通知前端更新执行状态
            io.emit('xianyu_execution_update', {
              taskId: log.task_id,
              deviceId: deviceId,
              status: 'failed',
              progress: 0,
              stage: 'error',
              message: '设备断开连接，任务执行失败',
              timestamp: new Date()
            });
          }

          // 发送设备状态重置通知给前端
          io.emit('device_execution_reset', {
            deviceId: deviceId,
            type: 'xianyu',
            affectedTasks: runningLogs.data.length,
            reason: 'device_disconnected',
            timestamp: new Date()
          });
        } else {
          console.log(`设备 ${deviceId} 没有正在执行的闲鱼任务`);
        }
      } catch (error) {
        console.error(`清理设备 ${deviceId} 闲鱼任务失败:`, error);
      }
    }
  }

  console.log('✅ 服务器核心模块设置完成');

  // 认证数据
  const authData = {
    authenticateToken
  };

  // 返回共享数据和实例
  const coreData = {
    pool,
    xiaohongshuLogService,
    xianyuLogService,
    xianyuChatService,
    devices,
    webClients,
    logs,
    pendingCommands,
    deviceCommands,
    recentlyDisconnectedDevices,
    throttledLog,
    upload,
    videoUpload,
    logThrottleMap,
    LOG_THROTTLE_INTERVAL,
    authData,
    cleanupXiaohongshuTasksForDevice,
    cleanupXianyuTasksForDevice
  };

  return { app, server, io, coreData, authData };
}

module.exports = { setupServerCore };
