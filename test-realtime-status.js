/**
 * 测试实时状态API的设备认证功能
 */

const http = require('http');

// 测试配置
const config = {
  serverHost: 'localhost',
  serverPort: 3002,
  deviceId: 'device_192_168_1_224', // 使用已注册的设备ID
  taskId: 'xiaohongshu_videoPublish_batch_' + Date.now() + '_device_192_168_1_224'
};

// 发送HTTP请求的工具函数
function makeRequest(path, method = 'POST', data = null, headers = {}) {
  return new Promise((resolve, reject) => {
    const postData = data ? JSON.stringify(data) : null;
    
    const options = {
      hostname: config.serverHost,
      port: config.serverPort,
      path: path,
      method: method,
      headers: {
        'Content-Type': 'application/json',
        'X-Device-Id': config.deviceId, // 使用设备ID认证
        ...headers
      }
    };

    const req = http.request(options, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        try {
          const parsedData = responseData ? JSON.parse(responseData) : {};
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            data: parsedData
          });
        } catch (error) {
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            data: responseData
          });
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    if (postData) {
      req.write(postData);
    }
    
    req.end();
  });
}

// 发送实时状态更新
async function sendRealtimeStatus(statusData) {
  const data = {
    deviceId: config.deviceId,
    taskId: config.taskId,
    publishedVideoCount: statusData.publishedVideoCount || 0,
    totalVideoCount: statusData.totalVideoCount || 1,
    currentStep: statusData.currentStep || '测试步骤',
    currentStatus: statusData.currentStatus || 'processing',
    errorMessage: statusData.errorMessage || '',
    message: statusData.message || '测试消息',
    timestamp: new Date().toISOString()
  };

  console.log('📤 发送实时状态:', data);
  
  try {
    const result = await makeRequest('/api/xiaohongshu/realtime-status', 'POST', data);
    console.log('📊 响应状态码:', result.statusCode);
    console.log('📊 响应数据:', result.data);
    return result;
  } catch (error) {
    console.error('❌ 发送实时状态失败:', error.message);
    throw error;
  }
}

// 主测试函数
async function testRealtimeStatus() {
  console.log('🧪 开始测试实时状态API...');
  console.log(`📱 使用设备ID: ${config.deviceId}`);
  console.log(`🎬 使用任务ID: ${config.taskId}`);
  console.log('');

  try {
    // 测试1: 发送初始状态
    console.log('1. 测试发送初始状态...');
    await sendRealtimeStatus({
      publishedVideoCount: 0,
      totalVideoCount: 1,
      currentStep: '等待脚本获取',
      currentStatus: 'pending',
      message: '任务已下发，等待设备获取脚本...'
    });
    
    await new Promise(resolve => setTimeout(resolve, 2000));

    // 测试2: 发送下载进度
    console.log('2. 测试发送下载进度...');
    await sendRealtimeStatus({
      publishedVideoCount: 0,
      totalVideoCount: 1,
      currentStep: '下载文件',
      currentStatus: 'processing',
      message: '正在下载: 15.2MB / 37.2MB'
    });
    
    await new Promise(resolve => setTimeout(resolve, 2000));

    // 测试3: 发送发布进度
    console.log('3. 测试发送发布进度...');
    await sendRealtimeStatus({
      publishedVideoCount: 0,
      totalVideoCount: 1,
      currentStep: '发布视频',
      currentStatus: 'processing',
      message: '正在发布视频到小红书...'
    });
    
    await new Promise(resolve => setTimeout(resolve, 2000));

    // 测试4: 发送完成状态
    console.log('4. 测试发送完成状态...');
    await sendRealtimeStatus({
      publishedVideoCount: 1,
      totalVideoCount: 1,
      currentStep: '发布完成',
      currentStatus: 'completed',
      message: '视频发布成功！'
    });

    console.log('\n✅ 实时状态API测试完成');

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error.message);
  }
}

// 运行测试
testRealtimeStatus();
