"use strict";(self["webpackChunkautojs_web_control"]=self["webpackChunkautojs_web_control"]||[]).push([[171],{171:function(e,t,s){s.r(t),s.d(t,{default:function(){return c}});var o=function(){var e=this,t=e._self._c;return t("div",{staticClass:"xiaohongshu-logs"},[t("el-card",[t("div",{staticClass:"card-header",attrs:{slot:"header"},slot:"header"},[t("span",[e._v("小红书自动化执行日志")]),t("div",[t("el-button",{attrs:{type:"primary",icon:"el-icon-refresh",loading:e.loading},on:{click:e.refreshLogs}},[e._v(" 刷新 ")]),t("el-button",{attrs:{type:"danger",icon:"el-icon-delete"},on:{click:e.clearLogs}},[e._v(" 清空日志 ")])],1)]),t("div",{staticClass:"filter-section"},[t("el-form",{attrs:{model:e.filterForm,inline:""}},[t("el-form-item",{attrs:{label:"功能类型"}},[t("el-select",{attrs:{placeholder:"选择功能类型",clearable:""},model:{value:e.filterForm.functionType,callback:function(t){e.$set(e.filterForm,"functionType",t)},expression:"filterForm.functionType"}},[t("el-option",{attrs:{label:"修改资料",value:"profile"}}),t("el-option",{attrs:{label:"搜索加群",value:"groupChat"}}),t("el-option",{attrs:{label:"循环群发",value:"groupMessage"}}),t("el-option",{attrs:{label:"文章评论",value:"articleComment"}}),t("el-option",{attrs:{label:"UID私信",value:"uidMessage"}})],1)],1),t("el-form-item",{attrs:{label:"设备"}},[t("el-select",{attrs:{placeholder:"选择设备",clearable:""},model:{value:e.filterForm.deviceId,callback:function(t){e.$set(e.filterForm,"deviceId",t)},expression:"filterForm.deviceId"}},e._l(e.devices,function(e){return t("el-option",{key:e.device_id,attrs:{label:e.device_name,value:e.device_id}})}),1)],1),t("el-form-item",{attrs:{label:"执行状态"}},[t("el-select",{attrs:{placeholder:"选择状态",clearable:""},model:{value:e.filterForm.executionStatus,callback:function(t){e.$set(e.filterForm,"executionStatus",t)},expression:"filterForm.executionStatus"}},[t("el-option",{attrs:{label:"等待中",value:"pending"}}),t("el-option",{attrs:{label:"执行中",value:"running"}}),t("el-option",{attrs:{label:"已完成",value:"completed"}}),t("el-option",{attrs:{label:"已失败",value:"failed"}}),t("el-option",{attrs:{label:"已停止",value:"stopped"}})],1)],1),t("el-form-item",{attrs:{label:"时间范围"}},[t("el-date-picker",{attrs:{type:"datetimerange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",format:"yyyy-MM-dd HH:mm:ss","value-format":"yyyy-MM-dd HH:mm:ss"},model:{value:e.dateRange,callback:function(t){e.dateRange=t},expression:"dateRange"}})],1),t("el-form-item",[t("el-button",{attrs:{type:"primary"},on:{click:e.searchLogs}},[e._v("搜索")]),t("el-button",{on:{click:e.resetFilter}},[e._v("重置")])],1)],1)],1),t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticStyle:{width:"100%"},attrs:{data:e.logs,stripe:""}},[t("el-table-column",{attrs:{prop:"task_id",label:"任务ID",width:"200","show-overflow-tooltip":""}}),t("el-table-column",{attrs:{prop:"function_type",label:"功能类型",width:"120"},scopedSlots:e._u([{key:"default",fn:function(s){return[t("el-tag",{attrs:{type:e.getFunctionTypeColor(s.row.function_type)}},[e._v(" "+e._s(e.getFunctionTypeName(s.row.function_type))+" ")])]}}])}),t("el-table-column",{attrs:{prop:"device_name",label:"设备名称",width:"150","show-overflow-tooltip":""}}),t("el-table-column",{attrs:{prop:"selected_app",label:"小红书应用",width:"120","show-overflow-tooltip":""},scopedSlots:e._u([{key:"default",fn:function(s){return[s.row.selected_app&&"默认"!==s.row.selected_app?t("span",[e._v(" "+e._s(s.row.selected_app)+" ")]):t("span",{staticStyle:{color:"#909399"}},[e._v("默认")])]}}])}),t("el-table-column",{attrs:{prop:"execution_status",label:"执行状态",width:"100"},scopedSlots:e._u([{key:"default",fn:function(s){return[t("el-tag",{attrs:{type:e.getStatusColor(s.row.execution_status)}},[e._v(" "+e._s(e.getStatusName(s.row.execution_status))+" ")])]}}])}),t("el-table-column",{attrs:{prop:"progress_percentage",label:"进度",width:"100"},scopedSlots:e._u([{key:"default",fn:function(s){return[t("el-progress",{attrs:{percentage:s.row.progress_percentage||0,status:e.getProgressStatus(s.row.execution_status),"stroke-width":8}})]}}])}),t("el-table-column",{attrs:{prop:"started_at",label:"开始时间",width:"180"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e.$moment(t.row.started_at).format("YYYY-MM-DD HH:mm:ss"))+" ")]}}])}),t("el-table-column",{attrs:{prop:"execution_duration",label:"执行时长",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e.formatDuration(t.row.execution_duration))+" ")]}}])}),t("el-table-column",{attrs:{label:"操作",width:"200",fixed:"right"},scopedSlots:e._u([{key:"default",fn:function(s){return["running"===s.row.execution_status?t("el-button",{staticStyle:{color:"#f56c6c"},attrs:{type:"text",size:"small",loading:s.row.stopping},on:{click:function(t){return e.stopExecution(s.row)}}},[e._v(" 停止 ")]):e._e(),["completed","failed","stopped"].includes(s.row.execution_status)?t("el-button",{staticStyle:{color:"#409eff"},attrs:{type:"text",size:"small"},on:{click:function(t){return e.retryExecution(s.row)}}},[e._v(" 再来一次 ")]):e._e(),t("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return e.viewLogDetail(s.row)}}},[e._v(" 查看详情 ")])]}}])})],1),t("div",{staticClass:"pagination-wrapper"},[t("el-pagination",{attrs:{"current-page":e.currentPage,"page-sizes":[10,20,50,100],"page-size":e.pageSize,layout:"total, sizes, prev, pager, next, jumper",total:e.total},on:{"size-change":e.handleSizeChange,"current-change":e.handlePageChange}})],1)],1),t("el-dialog",{attrs:{title:"执行日志详情",visible:e.detailDialogVisible,width:"80%","close-on-click-modal":!1},on:{"update:visible":function(t){e.detailDialogVisible=t}}},[e.currentLog?t("div",{staticClass:"log-detail"},[t("el-descriptions",{attrs:{column:2,border:""}},[t("el-descriptions-item",{attrs:{label:"任务ID"}},[e._v(e._s(e.currentLog.task_id))]),t("el-descriptions-item",{attrs:{label:"功能类型"}},[t("el-tag",{attrs:{type:e.getFunctionTypeColor(e.currentLog.function_type)}},[e._v(" "+e._s(e.getFunctionTypeName(e.currentLog.function_type))+" ")])],1),t("el-descriptions-item",{attrs:{label:"设备名称"}},[e._v(e._s(e.currentLog.device_name))]),t("el-descriptions-item",{attrs:{label:"设备ID"}},[e._v(e._s(e.currentLog.device_id))]),t("el-descriptions-item",{attrs:{label:"小红书应用"}},[e.currentLog.selected_app&&"默认"!==e.currentLog.selected_app?t("span",[e._v(" "+e._s(e.currentLog.selected_app)+" ")]):t("span",{staticStyle:{color:"#909399"}},[e._v("默认")])]),t("el-descriptions-item",{attrs:{label:"执行状态"}},[t("el-tag",{attrs:{type:e.getStatusColor(e.currentLog.execution_status)}},[e._v(" "+e._s(e.getStatusName(e.currentLog.execution_status))+" ")])],1),t("el-descriptions-item",{attrs:{label:"进度"}},[e._v(e._s(e.currentLog.progress_percentage||0)+"%")]),t("el-descriptions-item",{attrs:{label:"开始时间"}},[e._v(" "+e._s(e.$moment(e.currentLog.started_at).format("YYYY-MM-DD HH:mm:ss"))+" ")]),t("el-descriptions-item",{attrs:{label:"完成时间"}},[e._v(" "+e._s(e.currentLog.completed_at?e.$moment(e.currentLog.completed_at).format("YYYY-MM-DD HH:mm:ss"):"未完成")+" ")]),t("el-descriptions-item",{attrs:{label:"执行时长"}},[e._v(" "+e._s(e.formatDuration(e.currentLog.execution_duration))+" ")])],1),t("div",{staticClass:"config-section"},[t("h4",[e._v("配置参数")]),t("pre",{staticClass:"config-content"},[e._v(e._s(JSON.stringify(e.currentLog.config_params,null,2)))])]),e.currentLog.schedule_config?t("div",{staticClass:"config-section"},[t("h4",[e._v("调度配置")]),t("pre",{staticClass:"config-content"},[e._v(e._s(JSON.stringify(e.currentLog.schedule_config,null,2)))])]):e._e(),e.currentLog.execution_result?t("div",{staticClass:"config-section"},[t("h4",[e._v("执行结果")]),t("pre",{staticClass:"config-content"},[e._v(e._s(JSON.stringify(e.currentLog.execution_result,null,2)))])]):e._e(),e.currentLog.execution_logs?t("div",{staticClass:"config-section"},[t("h4",[e._v("执行日志")]),t("div",{staticClass:"log-content"},[t("pre",[e._v(e._s(e.currentLog.execution_logs))])])]):e._e(),e.currentLog.error_message?t("div",{staticClass:"config-section"},[t("h4",[e._v("错误信息")]),t("el-alert",{attrs:{title:e.currentLog.error_message,type:"error",closable:!1,"show-icon":""}})],1):e._e()],1):e._e(),t("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.detailDialogVisible=!1}}},[e._v("关闭")])],1)])],1)},a=[],i={name:"XiaohongshuLogs",data(){return{logs:[],loading:!1,detailDialogVisible:!1,currentLog:null,currentPage:1,pageSize:20,total:0,dateRange:[],filterForm:{functionType:"",deviceId:"",executionStatus:""},refreshTimer:null,refreshInterval:6e3,isAutoRefreshEnabled:!0,stopPollingTimers:new Map}},computed:{devices(){return this.$store.getters["device/devices"]}},async created(){await this.loadData(),this.startAutoRefresh()},beforeDestroy(){this.stopAutoRefresh(),this.clearAllStopPolling()},methods:{async loadData(){await this.$store.dispatch("device/fetchDevices"),await this.loadLogs()},async loadLogs(){this.loading=!0;try{const e={page:this.currentPage,limit:this.pageSize,...this.filterForm};this.dateRange&&2===this.dateRange.length&&(e.startDate=this.dateRange[0],e.endDate=this.dateRange[1]);const t=await this.$http.get("/api/xiaohongshu/logs",{params:e});t.data.success?(this.logs=t.data.data.logs||[],this.total=t.data.data.total||0):this.$message.error("加载日志失败: "+t.data.message)}catch(e){console.error("加载小红书执行日志失败:",e),this.$message.error("加载日志失败")}finally{this.loading=!1}},async refreshLogs(){await this.loadLogs()},startAutoRefresh(){this.isAutoRefreshEnabled&&(console.log(`[XiaohongshuLogs] 开始自动刷新，间隔: ${this.refreshInterval}ms`),this.refreshTimer=setInterval(()=>{this.autoRefreshLogs()},this.refreshInterval))},stopAutoRefresh(){this.refreshTimer&&(console.log("[XiaohongshuLogs] 停止自动刷新"),clearInterval(this.refreshTimer),this.refreshTimer=null)},async autoRefreshLogs(){try{const e={page:this.currentPage,limit:this.pageSize,...this.filterForm};this.dateRange&&2===this.dateRange.length&&(e.startDate=this.dateRange[0],e.endDate=this.dateRange[1]);const t=await this.$http.get("/api/xiaohongshu/logs",{params:e});if(t.data.success){const e=[...this.logs];this.logs=t.data.data.logs||[],this.total=t.data.data.total||0,this.checkForStatusChanges(e,this.logs),console.log("[XiaohongshuLogs] 执行日志自动刷新完成，当前日志数量:",this.logs.length)}}catch(e){console.error("[XiaohongshuLogs] 自动刷新失败:",e)}},checkForStatusChanges(e,t){try{t.forEach(t=>{const s=e.find(e=>e.log_id===t.log_id);s&&"stopped"!==s.execution_status&&"stopped"===t.execution_status&&(console.log(`[状态变化检测] 检测到状态变为stopped: ${t.log_id}`),console.log(`[状态变化检测] 旧状态: ${s.execution_status}, 新状态: ${t.execution_status}`),this.clearStopPolling(t.log_id),this.notifyScriptStopped(t),this.$message.success(`脚本 ${t.function_name} 已成功停止`))})}catch(s){console.error("[状态变化检测] 检测失败:",s)}},startStopStatusPolling(e){console.log(`[停止轮询] 开始轮询停止状态: ${e}`),this.clearStopPolling(e);let t=0;const s=20,o=setInterval(async()=>{t++,console.log(`[停止轮询] 第${t}次轮询: ${e}`);try{await this.loadLogs();const o=this.logs.find(t=>t.log_id===e);if(o){if(console.log(`[停止轮询] 当前状态: ${o.execution_status}, 进度: ${o.progress_percentage}%`),"stopped"===o.execution_status)return console.log(`[停止轮询] 停止完成: ${e}`),this.clearStopPolling(e),this.$message.success("脚本已成功停止"),void this.notifyScriptStopped(o)}else console.warn(`[停止轮询] 未找到日志记录: ${e}`);t>=s&&(console.log(`[停止轮询] 达到最大轮询次数，停止轮询: ${e}`),this.clearStopPolling(e),this.$message.warning("停止状态检查超时，请手动刷新查看最新状态"))}catch(o){console.error(`[停止轮询] 轮询失败: ${e}`,o)}},2e3);this.stopPollingTimers.set(e,o)},clearStopPolling(e){const t=this.stopPollingTimers.get(e);t&&(clearInterval(t),this.stopPollingTimers.delete(e),console.log(`[停止轮询] 已清除轮询定时器: ${e}`))},clearAllStopPolling(){console.log(`[停止轮询] 清除所有轮询定时器，数量: ${this.stopPollingTimers.size}`);for(const[e,t]of this.stopPollingTimers)clearInterval(t),console.log(`[停止轮询] 已清除: ${e}`);this.stopPollingTimers.clear()},async stopExecution(e){try{console.log("[停止执行] 开始停止脚本:",e.log_id),this.$set(e,"stopping",!0);const t=await this.$http.post("/api/xiaohongshu/stop",{taskId:e.task_id,deviceId:e.device_id,logId:e.log_id});t.data.success?(this.$message.success("停止命令已发送"),console.log("[停止执行] 停止命令发送成功:",e.log_id),e.execution_status="stopping",e.status_message="正在停止...",e.progress_percentage=50,this.notifyScriptStopped(e),this.startStopStatusPolling(e.log_id)):(console.error("[停止执行] 停止命令发送失败:",t.data.message),this.$message.error(t.data.message||"停止失败"))}catch(t){console.error("[停止执行] 停止执行失败:",t),this.$message.error("停止失败: "+(t.response?.data?.message||t.message))}finally{this.$set(e,"stopping",!1)}},notifyScriptStopped(e){try{console.log("[执行日志] 通知脚本停止:",e);const t={"修改资料":"profile","搜索加群":"searchGroupChat","循环群发":"groupMessage","文章评论":"articleComment","手动输入UID私信":"uidMessage","文件上传UID私信":"uidFileMessage","发布视频":"videoPublish","UID私信":"uidMessage"},s=t[e.function_name];s?(console.log(`[执行日志] 发送停止事件: ${s}`),this.$root.$emit("xiaohongshu-task-stopped",{functionType:s,reason:"stopped_from_logs",logId:e.log_id,taskId:e.task_id,deviceId:e.device_id}),this.$store.dispatch("xiaohongshu/stopTask",{functionType:s,reason:"stopped_from_logs"}),setTimeout(()=>{console.log(`[执行日志] 延迟发送停止事件: ${s}`),this.$root.$emit("xiaohongshu-task-stopped",{functionType:s,reason:"stopped_from_logs_delayed",logId:e.log_id,taskId:e.task_id,deviceId:e.device_id})},500),console.log(`[执行日志] 已通知 ${s} 功能重置状态`)):console.warn("[执行日志] 未知的功能名称:",e.function_name)}catch(t){console.error("[执行日志] 通知脚本停止失败:",t)}},async retryExecution(e){try{const s=await this.$confirm(`确定要重新执行"${this.getFunctionTypeName(e.function_type)}"功能吗？`,"确认重试",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});if(s){const s={name:"XiaohongshuAutomation",query:{retry:"true",functionType:e.function_type,deviceId:e.device_id,originalTaskId:e.task_id}};if(e.config_params)try{const t=JSON.parse(e.config_params);s.query.config=JSON.stringify(t)}catch(t){console.warn("解析配置参数失败:",t)}this.$router.push(s),this.$message.info("正在跳转到小红书自动化页面...")}}catch(s){"cancel"!==s&&(console.error("重试执行失败:",s),this.$message.error("操作失败"))}},async clearLogs(){try{await this.$confirm("确定要清空所有小红书执行日志吗？此操作不可恢复！","确认清空",{type:"warning"});const e=await this.$http.delete("/api/xiaohongshu/logs");e.data.success?(this.$message.success(e.data.message),await this.loadLogs()):this.$message.error("清空失败: "+e.data.message)}catch(e){"cancel"!==e&&this.$message.error("清空失败: "+e.message)}},searchLogs(){this.currentPage=1,this.loadLogs()},resetFilter(){this.filterForm={functionType:"",deviceId:"",executionStatus:""},this.dateRange=[],this.currentPage=1,this.loadLogs()},handlePageChange(e){this.currentPage=e,this.loadLogs()},handleSizeChange(e){this.pageSize=e,this.currentPage=1,this.loadLogs()},viewLogDetail(e){this.currentLog=e,this.detailDialogVisible=!0},getFunctionTypeName(e){const t={profile:"修改资料",searchGroupChat:"搜索加群",groupMessage:"循环群发",articleComment:"文章评论",uidMessage:"手动输入UID私信",uidFileMessage:"文件上传UID私信",videoPublish:"发布视频"};return t[e]||e},getFunctionTypeColor(e){const t={profile:"primary",searchGroupChat:"success",groupMessage:"warning",articleComment:"info",uidMessage:"danger",uidFileMessage:"warning"};return t[e]||""},getStatusName(e){const t={pending:"等待中",running:"执行中",completed:"已完成",failed:"已失败",stopped:"已停止"};return t[e]||e},getStatusColor(e){const t={pending:"info",running:"warning",completed:"success",failed:"danger",stopped:"info"};return t[e]||""},getProgressStatus(e){return"completed"===e?"success":"failed"===e?"exception":null},formatDuration(e){if(!e)return"0秒";const t=Math.floor(e/3600),s=Math.floor(e%3600/60),o=e%60;return t>0?`${t}小时${s}分${o}秒`:s>0?`${s}分${o}秒`:`${o}秒`}}},r=i,l=s(1656),n=(0,l.A)(r,o,a,!1,null,"10e56b1c",null),c=n.exports}}]);