#!/usr/bin/env node

/**
 * Auto.js云群控系统宝塔面板部署配置脚本
 * 专门针对宝塔面板环境的配置工具
 */

const fs = require('fs');
const path = require('path');
const readline = require('readline');

// 创建命令行接口
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// 宝塔面板默认路径
const BT_PATHS = {
  linux: '/www/wwwroot',
  windows: 'D:\\BtSoft\\wwwroot'
};

// 配置文件路径
const CONFIG_FILES = {
  database: 'server/config/database.js',
  vueConfig: 'web/vue.config.js',
  serverConfig: 'web/src/utils/serverConfig.js',
  deviceScript: 'scripts/双向.js',
  serverCore: 'server/core/server-core.js'
};

// 提示用户输入配置信息
function promptConfig() {
  return new Promise((resolve) => {
    const config = {};
    
    console.log('\n🚀 Auto.js云群控系统宝塔面板部署配置工具');
    console.log('='.repeat(60));
    console.log('💡 专为宝塔面板环境优化的配置脚本');
    
    rl.question('请输入服务器公网IP地址或域名: ', (serverHost) => {
      config.serverHost = serverHost;
      
      rl.question('请输入网站目录名称 (默认: autojs-control): ', (siteName) => {
        config.siteName = siteName || 'autojs-control';
        
        rl.question('请输入数据库密码 (在宝塔面板创建数据库时设置的密码): ', (dbPassword) => {
          config.dbPassword = dbPassword;
          
          rl.question('请输入数据库用户名 (默认: autojs_control): ', (dbUser) => {
            config.dbUser = dbUser || 'autojs_control';
            
            rl.question('请输入数据库名称 (默认: autojs_control): ', (dbName) => {
              config.dbName = dbName || 'autojs_control';
              
              rl.question('请输入服务器端口 (默认: 3002): ', (serverPort) => {
                config.serverPort = serverPort || '3002';
                
                rl.question('请选择操作系统 (1: Linux, 2: Windows, 默认: 1): ', (osChoice) => {
                  config.osType = osChoice === '2' ? 'windows' : 'linux';
                  config.btPath = BT_PATHS[config.osType];
                  
                  rl.close();
                  resolve(config);
                });
              });
            });
          });
        });
      });
    });
  });
}

// 备份原文件
function backupFile(filePath) {
  if (fs.existsSync(filePath)) {
    const backupPath = filePath + '.bt-backup.' + Date.now();
    fs.copyFileSync(filePath, backupPath);
    console.log(`✅ 已备份: ${filePath} -> ${backupPath}`);
  }
}

// 修改数据库配置文件（宝塔环境）
function updateDatabaseConfig(config) {
  const filePath = CONFIG_FILES.database;
  if (!fs.existsSync(filePath)) {
    console.log(`❌ 文件不存在: ${filePath}`);
    return;
  }
  
  backupFile(filePath);
  
  let content = fs.readFileSync(filePath, 'utf8');
  
  // 宝塔环境数据库配置
  content = content.replace(
    /host:\s*['"`].*?['"`]/,
    `host: 'localhost'`  // 宝塔MySQL默认localhost
  );
  content = content.replace(
    /user:\s*['"`].*?['"`]/,
    `user: '${config.dbUser}'`
  );
  content = content.replace(
    /password:\s*['"`].*?['"`]/,
    `password: '${config.dbPassword}'`
  );
  content = content.replace(
    /database:\s*['"`].*?['"`]/,
    `database: '${config.dbName}'`
  );
  
  fs.writeFileSync(filePath, content);
  console.log(`✅ 已更新数据库配置: ${filePath}`);
}

// 修改Vue配置文件（宝塔环境）
function updateVueConfig(config) {
  const filePath = CONFIG_FILES.vueConfig;
  if (!fs.existsSync(filePath)) {
    console.log(`❌ 文件不存在: ${filePath}`);
    return;
  }
  
  backupFile(filePath);
  
  let content = fs.readFileSync(filePath, 'utf8');
  
  // 宝塔环境代理配置
  const serverUrl = `http://${config.serverHost}:${config.serverPort}`;
  content = content.replace(
    /target:\s*['"`]http:\/\/.*?['"`]/g,
    `target: '${serverUrl}'`
  );
  
  // 替换日志中的地址
  content = content.replace(
    /http:\/\/[\d\.:]+/g,
    serverUrl
  );
  content = content.replace(
    /ws:\/\/[\d\.:]+/g,
    `ws://${config.serverHost}:${config.serverPort}`
  );
  
  fs.writeFileSync(filePath, content);
  console.log(`✅ 已更新Vue配置: ${filePath}`);
}

// 修改前端服务器配置文件
function updateServerConfig(config) {
  const filePath = CONFIG_FILES.serverConfig;
  if (!fs.existsSync(filePath)) {
    console.log(`❌ 文件不存在: ${filePath}`);
    return;
  }
  
  backupFile(filePath);
  
  let content = fs.readFileSync(filePath, 'utf8');
  
  // 更新服务器地址
  const serverUrl = `http://${config.serverHost}:${config.serverPort}`;
  content = content.replace(
    /http:\/\/[\d\.:]+/g,
    serverUrl
  );
  content = content.replace(
    /https:\/\/[\d\.:]+/g,
    `https://${config.serverHost}:${config.serverPort}`
  );
  
  fs.writeFileSync(filePath, content);
  console.log(`✅ 已更新前端服务器配置: ${filePath}`);
}

// 修改设备端脚本配置
function updateDeviceScript(config) {
  const filePath = CONFIG_FILES.deviceScript;
  if (!fs.existsSync(filePath)) {
    console.log(`❌ 文件不存在: ${filePath}`);
    return;
  }
  
  backupFile(filePath);
  
  let content = fs.readFileSync(filePath, 'utf8');
  
  // 替换服务器URL
  const serverUrl = `http://${config.serverHost}:${config.serverPort}`;
  content = content.replace(
    /var serverUrl = ["']http:\/\/.*?["'];/,
    `var serverUrl = "${serverUrl}";`
  );
  
  fs.writeFileSync(filePath, content);
  console.log(`✅ 已更新设备端脚本配置: ${filePath}`);
}

// 创建宝塔PM2配置文件
function createBTPM2Config(config) {
  const projectPath = config.osType === 'windows' 
    ? `${config.btPath}\\${config.siteName}`
    : `${config.btPath}/${config.siteName}`;
    
  const pm2Config = `module.exports = {
  apps: [{
    name: 'autojs-control',
    script: 'server-main.js',
    cwd: '${projectPath}',
    instances: 1,
    exec_mode: 'fork',
    env: {
      NODE_ENV: 'development'
    },
    env_production: {
      NODE_ENV: 'production',
      SERVER_HOST: '${config.serverHost}',
      SERVER_PORT: ${config.serverPort},
      DB_HOST: 'localhost',
      DB_USER: '${config.dbUser}',
      DB_PASSWORD: '${config.dbPassword}',
      DB_NAME: '${config.dbName}'
    },
    error_file: './logs/err.log',
    out_file: './logs/out.log',
    log_file: './logs/combined.log',
    time: true,
    max_memory_restart: '1G',
    node_args: '--max_old_space_size=1024'
  }]
};
`;
  
  fs.writeFileSync('ecosystem.config.js', pm2Config);
  console.log(`✅ 已创建宝塔PM2配置: ecosystem.config.js`);
}

// 创建宝塔Nginx配置文件
function createBTNginxConfig(config) {
  const nginxConfig = `# Auto.js云群控系统 - 宝塔Nginx配置
# 请将此配置添加到宝塔面板 → 网站 → 设置 → 配置文件中

server {
    listen 80;
    server_name ${config.serverHost};
    
    # 网站根目录
    root ${config.btPath}/${config.siteName}/web/dist;
    index index.html index.htm;
    
    # 前端静态文件
    location / {
        try_files $uri $uri/ /index.html;
        
        # 静态资源缓存
        location ~* \\.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }
    
    # API反向代理
    location /api/ {
        proxy_pass http://127.0.0.1:${config.serverPort};
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 300s;
        proxy_connect_timeout 75s;
    }
    
    # WebSocket反向代理
    location /socket.io/ {
        proxy_pass http://127.0.0.1:${config.serverPort};
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_read_timeout 300s;
        proxy_connect_timeout 75s;
    }
    
    # 安全配置
    location ~ /\\. {
        deny all;
    }
    
    location ~ \\.(sql|log|conf)$ {
        deny all;
    }
}
`;
  
  fs.writeFileSync('bt-nginx.conf', nginxConfig);
  console.log(`✅ 已创建宝塔Nginx配置: bt-nginx.conf`);
}

// 创建宝塔部署脚本
function createBTDeployScript(config) {
  const isWindows = config.osType === 'windows';
  const scriptExt = isWindows ? '.bat' : '.sh';
  const scriptName = `bt-deploy${scriptExt}`;
  
  let deployScript;
  
  if (isWindows) {
    deployScript = `@echo off
echo 🚀 Auto.js云群控系统宝塔部署脚本 (Windows)
echo ================================================

echo 📦 安装后端依赖...
call npm install --production

echo 🏗️ 构建前端项目...
cd web
call npm install
call npm run build
cd ..

echo 📁 创建日志目录...
if not exist logs mkdir logs

echo ✅ 部署完成！
echo.
echo 📋 接下来的步骤：
echo 1. 在宝塔面板PM2管理器中添加项目
echo 2. 项目路径：${config.btPath}\\${config.siteName}
echo 3. 启动文件：server-main.js
echo 4. 复制bt-nginx.conf内容到网站配置文件
echo 5. 重载Nginx配置
echo.
echo 🌐 访问地址：http://${config.serverHost}
pause
`;
  } else {
    deployScript = `#!/bin/bash

echo "🚀 Auto.js云群控系统宝塔部署脚本 (Linux)"
echo "================================================"

# 设置环境变量
export NODE_ENV=production
export SERVER_HOST=${config.serverHost}
export SERVER_PORT=${config.serverPort}
export DB_HOST=localhost
export DB_USER=${config.dbUser}
export DB_PASSWORD=${config.dbPassword}
export DB_NAME=${config.dbName}

echo "📦 安装后端依赖..."
npm install --production

echo "🏗️ 构建前端项目..."
cd web
npm install
npm run build
cd ..

echo "📁 创建日志目录..."
mkdir -p logs

echo "✅ 部署完成！"
echo ""
echo "📋 接下来的步骤："
echo "1. 在宝塔面板PM2管理器中添加项目"
echo "2. 项目路径：${config.btPath}/${config.siteName}"
echo "3. 启动文件：server-main.js"
echo "4. 复制bt-nginx.conf内容到网站配置文件"
echo "5. 重载Nginx配置"
echo ""
echo "🌐 访问地址：http://${config.serverHost}"
`;
  }
  
  fs.writeFileSync(scriptName, deployScript);
  if (!isWindows) {
    fs.chmodSync(scriptName, '755');
  }
  console.log(`✅ 已创建宝塔部署脚本: ${scriptName}`);
}

// 创建数据库导入说明
function createDBImportGuide(config) {
  const guide = `# 数据库导入指南

## 在宝塔面板中导入数据库

### 方法1：使用phpMyAdmin
1. 宝塔面板 → 数据库 → 点击数据库名
2. 进入phpMyAdmin管理界面
3. 选择"导入"选项卡
4. 选择文件：初始化数据库.sql
5. 点击"执行"完成导入

### 方法2：使用命令行
\`\`\`bash
mysql -u ${config.dbUser} -p ${config.dbName} < 初始化数据库.sql
\`\`\`

### 方法3：使用宝塔数据库工具
1. 宝塔面板 → 数据库 → MySQL
2. 点击"导入"按钮
3. 选择数据库：${config.dbName}
4. 上传SQL文件：初始化数据库.sql
5. 点击"导入"

## 验证导入结果
导入成功后，数据库应包含以下表：
- users (用户表)
- devices (设备表)
- xiaohongshu_execution_logs (小红书执行日志)
- xianyu_execution_logs (闲鱼执行日志)
- 等共19个表

## 默认管理员账户
- 用户名：admin
- 密码：admin123

首次登录后请及时修改密码！
`;
  
  fs.writeFileSync('数据库导入指南.md', guide);
  console.log(`✅ 已创建数据库导入指南: 数据库导入指南.md`);
}

// 主函数
async function main() {
  try {
    const config = await promptConfig();
    
    console.log('\n📝 宝塔部署配置信息:');
    console.log(`服务器地址: ${config.serverHost}`);
    console.log(`网站目录: ${config.siteName}`);
    console.log(`宝塔路径: ${config.btPath}`);
    console.log(`数据库用户: ${config.dbUser}`);
    console.log(`数据库名称: ${config.dbName}`);
    console.log(`服务器端口: ${config.serverPort}`);
    console.log(`操作系统: ${config.osType}`);
    
    console.log('\n🔧 开始更新配置文件...');
    
    // 更新所有配置文件
    updateDatabaseConfig(config);
    updateVueConfig(config);
    updateServerConfig(config);
    updateDeviceScript(config);
    
    // 创建宝塔专用配置文件
    createBTPM2Config(config);
    createBTNginxConfig(config);
    createBTDeployScript(config);
    createDBImportGuide(config);
    
    console.log('\n🎉 宝塔部署配置完成！');
    console.log('\n📋 接下来的步骤:');
    console.log('1. 将项目文件上传到宝塔服务器');
    console.log(`2. 上传路径：${config.btPath}/${config.siteName}/`);
    console.log('3. 在宝塔面板创建数据库和网站');
    console.log('4. 导入数据库结构（参考：数据库导入指南.md）');
    console.log(`5. 运行部署脚本：${config.osType === 'windows' ? 'bt-deploy.bat' : './bt-deploy.sh'}`);
    console.log('6. 在宝塔PM2管理器中添加Node.js项目');
    console.log('7. 复制bt-nginx.conf内容到网站配置文件');
    console.log('8. 重载Nginx配置');
    console.log(`9. 访问系统：http://${config.serverHost}`);
    
    console.log('\n💡 宝塔面板操作提示:');
    console.log('• 软件商店安装：Nginx, MySQL, PM2管理器, Node.js版本管理器');
    console.log('• 安全设置开放端口：80, 443, 3002');
    console.log('• 建议配置SSL证书提升安全性');
    console.log('• 设置定时备份保护数据安全');
    
  } catch (error) {
    console.error('❌ 配置过程中出现错误:', error.message);
    process.exit(1);
  }
}

// 运行主函数
main();
