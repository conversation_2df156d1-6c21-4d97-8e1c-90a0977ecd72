# Auto.js云群控系统快速部署指南

## 🚀 一键部署流程

### 第一步：运行配置脚本
```bash
# 在项目根目录运行配置脚本
node deploy-config.js
```

按提示输入以下信息：
- **服务器公网IP地址**：你的云服务器公网IP
- **数据库服务器IP地址**：通常与服务器IP相同
- **数据库用户名**：默认 `autojs_control`
- **数据库密码**：设置一个强密码
- **数据库名称**：默认 `autojs_control`
- **服务器端口**：默认 `3002`

### 第二步：上传文件到服务器
```bash
# 方式1：使用scp上传
scp -r . user@your-server-ip:/opt/autojs-control/

# 方式2：使用git克隆
git clone your-repo-url /opt/autojs-control
cd /opt/autojs-control
```

### 第三步：在服务器上运行部署脚本
```bash
# 给脚本执行权限
chmod +x deploy.sh
chmod +x deploy-check.sh

# 运行部署脚本
./deploy.sh
```

### 第四步：配置Nginx
```bash
# 复制Nginx配置
sudo cp nginx-autojs-control.conf /etc/nginx/sites-available/

# 启用站点
sudo ln -s /etc/nginx/sites-available/nginx-autojs-control.conf /etc/nginx/sites-enabled/

# 测试配置
sudo nginx -t

# 重启Nginx
sudo systemctl reload nginx
```

### 第五步：检查部署状态
```bash
# 运行检查脚本
./deploy-check.sh

# 查看服务状态
pm2 status
pm2 logs autojs-control
```

## 🔧 手动部署流程（详细版）

### 环境准备

#### Linux服务器
```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装Node.js 16+
curl -fsSL https://deb.nodesource.com/setup_16.x | sudo -E bash -
sudo apt-get install -y nodejs

# 安装PM2
sudo npm install -g pm2

# 安装Nginx
sudo apt install nginx -y

# 安装MySQL
sudo apt install mysql-server -y
sudo mysql_secure_installation
```

#### Windows服务器
1. 下载安装 Node.js 16+ (https://nodejs.org/)
2. 安装PM2：`npm install -g pm2`
3. 下载安装 MySQL (https://dev.mysql.com/downloads/mysql/)
4. 配置防火墙开放端口：80, 443, 3002, 3306

### 数据库配置

```sql
-- 创建数据库
CREATE DATABASE autojs_control CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户
CREATE USER 'autojs_control'@'%' IDENTIFIED BY '你的密码';
GRANT ALL PRIVILEGES ON autojs_control.* TO 'autojs_control'@'%';
FLUSH PRIVILEGES;

-- 导入表结构
mysql -u autojs_control -p autojs_control < 初始化数据库.sql
```

### 配置文件修改

#### 1. 数据库配置 (server/config/database.js)
```javascript
const dbConfig = {
  host: '你的数据库IP',
  user: 'autojs_control',
  password: '你的数据库密码',
  database: 'autojs_control',
  charset: 'utf8mb4',
  timezone: '+08:00'
};
```

#### 2. 前端代理配置 (web/vue.config.js)
```javascript
proxy: {
  '/api': {
    target: 'http://你的服务器IP:3002',
    changeOrigin: true,
    ws: true
  }
}
```

#### 3. 设备端配置 (scripts/双向.js)
```javascript
var serverUrl = "http://你的服务器IP:3002";
```

### 服务部署

#### 构建前端
```bash
cd web
npm install
npm run build
```

#### 部署前端到Nginx
```bash
# 创建网站目录
sudo mkdir -p /var/www/autojs-control

# 复制文件
sudo cp -r web/dist/* /var/www/autojs-control/

# 设置权限
sudo chown -R www-data:www-data /var/www/autojs-control
```

#### 启动后端服务
```bash
# 安装依赖
npm install --production

# 启动PM2服务
pm2 start ecosystem.config.js --env production
pm2 save
pm2 startup
```

## 🔍 故障排除

### 常见问题及解决方案

#### 1. 端口被占用
```bash
# 查看端口占用
sudo lsof -i :3002
sudo netstat -tlnp | grep :3002

# 杀死占用进程
sudo kill -9 PID
```

#### 2. 数据库连接失败
```bash
# 检查MySQL服务
sudo systemctl status mysql

# 测试连接
mysql -h 你的数据库IP -u autojs_control -p

# 检查防火墙
sudo ufw status
sudo ufw allow 3306
```

#### 3. Nginx配置错误
```bash
# 测试配置
sudo nginx -t

# 查看错误日志
sudo tail -f /var/log/nginx/error.log

# 重启Nginx
sudo systemctl restart nginx
```

#### 4. PM2服务启动失败
```bash
# 查看详细日志
pm2 logs autojs-control --lines 50

# 重启服务
pm2 restart autojs-control

# 删除并重新启动
pm2 delete autojs-control
pm2 start ecosystem.config.js --env production
```

#### 5. 设备连接失败
- 检查服务器IP配置是否正确
- 确认防火墙已开放3002端口
- 验证WebSocket连接是否正常
- 检查设备端脚本中的服务器地址

### 性能优化

#### 1. 数据库优化
```sql
-- 添加索引
ALTER TABLE devices ADD INDEX idx_device_id (device_id);
ALTER TABLE xiaohongshu_execution_logs ADD INDEX idx_task_id (task_id);

-- 清理旧日志
DELETE FROM execution_logs WHERE created_at < DATE_SUB(NOW(), INTERVAL 30 DAY);
```

#### 2. 服务器优化
```bash
# 增加文件描述符限制
echo "* soft nofile 65536" >> /etc/security/limits.conf
echo "* hard nofile 65536" >> /etc/security/limits.conf

# 优化内核参数
echo "net.core.somaxconn = 65536" >> /etc/sysctl.conf
sysctl -p
```

#### 3. Nginx优化
```nginx
# 在nginx.conf中添加
worker_processes auto;
worker_connections 1024;

# 启用gzip压缩
gzip on;
gzip_types text/plain text/css application/json application/javascript;
```

## 📊 监控和维护

### 日志管理
```bash
# PM2日志
pm2 logs autojs-control
pm2 flush  # 清空日志

# Nginx日志
sudo tail -f /var/log/nginx/access.log
sudo tail -f /var/log/nginx/error.log

# 系统日志
sudo journalctl -u nginx -f
sudo journalctl -u mysql -f
```

### 备份策略
```bash
# 数据库备份
mysqldump -u autojs_control -p autojs_control > backup_$(date +%Y%m%d).sql

# 代码备份
tar -czf autojs-control-backup-$(date +%Y%m%d).tar.gz /opt/autojs-control

# 自动备份脚本
echo "0 2 * * * mysqldump -u autojs_control -p密码 autojs_control > /backup/db_\$(date +\%Y\%m\%d).sql" | crontab -
```

### 更新部署
```bash
# 拉取最新代码
git pull origin main

# 重新构建前端
cd web && npm run build

# 重启服务
pm2 restart autojs-control

# 重新加载Nginx
sudo systemctl reload nginx
```

## 🔒 安全配置

### SSL证书配置
```bash
# 安装Certbot
sudo apt install certbot python3-certbot-nginx

# 获取证书
sudo certbot --nginx -d 你的域名

# 自动续期
echo "0 12 * * * /usr/bin/certbot renew --quiet" | sudo crontab -
```

### 防火墙配置
```bash
# 基本防火墙规则
sudo ufw default deny incoming
sudo ufw default allow outgoing
sudo ufw allow ssh
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw allow 3002/tcp
sudo ufw enable
```

### 访问控制
```nginx
# 在Nginx配置中限制管理页面访问
location /admin {
    allow 你的IP地址;
    deny all;
    # 其他配置...
}
```

## 📞 技术支持

如果在部署过程中遇到问题，可以：

1. 运行 `./deploy-check.sh` 检查系统状态
2. 查看详细的错误日志
3. 检查配置文件是否正确
4. 确认网络连接和防火墙设置
5. 验证数据库连接和权限

部署完成后，访问 `http://你的服务器IP` 即可使用系统！
