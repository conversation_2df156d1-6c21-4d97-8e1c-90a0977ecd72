# Auto.js云群控系统宝塔面板部署流程

## 📋 宝塔部署概述

宝塔面板提供了可视化的服务器管理界面，让部署变得更加简单。本文档详细说明如何在宝塔面板环境下部署Auto.js云群控系统。

## 🎯 宝塔环境要求

### 服务器配置
- **操作系统**：Linux (Ubuntu/CentOS) 或 Windows
- **内存**：建议2GB以上
- **硬盘**：建议20GB以上可用空间
- **网络**：公网IP，带宽建议5Mbps以上

### 宝塔面板版本
- **Linux版本**：7.7.0 或更高版本
- **Windows版本**：6.8 或更高版本

## 🚀 第一步：安装宝塔面板

### Linux服务器安装宝塔
```bash
# CentOS安装命令
yum install -y wget && wget -O install.sh http://download.bt.cn/install/install_6.0.sh && sh install.sh ed8484bec

# Ubuntu安装命令
wget -O install.sh http://download.bt.cn/install/install-ubuntu_6.0.sh && sudo bash install.sh ed8484bec

# Debian安装命令
wget -O install.sh http://download.bt.cn/install/install-ubuntu_6.0.sh && bash install.sh ed8484bec
```

### Windows服务器安装宝塔
1. 下载宝塔Windows面板：http://download.bt.cn/win/panel/BtSoft.zip
2. 解压到D盘根目录（D:\BtSoft\）
3. 运行BtSoft.exe安装
4. 访问 http://localhost:888 进入面板

## 🔧 第二步：宝塔面板环境配置

### 安装必要软件
在宝塔面板 → 软件商店 → 一键安装：

#### 必装软件
- **Nginx** 1.20+ （Web服务器）
- **MySQL** 5.7+ 或 8.0 （数据库）
- **PM2管理器** 4.0+ （Node.js进程管理）
- **Node.js版本管理器** （安装Node.js 16+）

#### 安装步骤
1. **安装Nginx**
   - 软件商店 → Web服务器 → Nginx → 安装
   - 选择编译安装（推荐）或极速安装

2. **安装MySQL**
   - 软件商店 → 数据库 → MySQL → 安装
   - 选择MySQL 8.0版本
   - 设置root密码（记住此密码）

3. **安装PM2管理器**
   - 软件商店 → 系统工具 → PM2管理器 → 安装

4. **安装Node.js**
   - 软件商店 → 运行环境 → Node.js版本管理器 → 安装
   - 安装Node.js 16.x或18.x版本

### 配置安全组和防火墙
在宝塔面板 → 安全：
- 开放端口：80, 443, 3002, 8888
- 如果是云服务器，还需在云服务商控制台开放相同端口

## 🗄️ 第三步：创建数据库

### 在宝塔面板创建数据库
1. **进入数据库管理**
   - 宝塔面板 → 数据库 → MySQL

2. **创建数据库**
   - 点击"添加数据库"
   - 数据库名：`autojs_control`
   - 用户名：`autojs_control`
   - 密码：设置强密码（记住此密码）
   - 访问权限：选择"所有人"或指定IP

3. **导入数据库结构**
   - 点击数据库名进入phpMyAdmin
   - 选择"导入"选项卡
   - 上传并导入 `初始化数据库.sql` 文件

## 📁 第四步：创建网站和上传代码

### 创建网站
1. **添加站点**
   - 宝塔面板 → 网站 → 添加站点
   - 域名：填入你的域名或服务器IP
   - 根目录：`/www/wwwroot/autojs-control`
   - PHP版本：选择"纯静态"
   - 数据库：选择刚创建的数据库

2. **配置网站目录**
   - 网站设置 → 网站目录
   - 运行目录：设置为 `/dist`（前端构建后的目录）
   - 取消勾选"防跨站攻击"

### 上传项目代码
1. **使用宝塔文件管理器**
   - 宝塔面板 → 文件
   - 进入 `/www/wwwroot/autojs-control`
   - 上传项目压缩包并解压

2. **或使用FTP/SFTP上传**
   - 宝塔面板 → FTP → 添加FTP账号
   - 使用FTP工具上传整个项目

### 项目目录结构
```
/www/wwwroot/autojs-control/
├── server/                 # 后端代码
├── web/                   # 前端代码
├── jb/                    # 小红书脚本
├── xy-jb/                 # 闲鱼脚本
├── scripts/               # 工具脚本
├── config/                # 配置文件
├── package.json           # 依赖配置
└── 初始化数据库.sql        # 数据库文件
```

## ⚙️ 第五步：修改配置文件

### 方式1：使用自动配置脚本
在项目根目录运行：
```bash
# 进入项目目录
cd /www/wwwroot/autojs-control

# 运行配置脚本
node deploy-config.js
```

### 方式2：手动修改配置文件

#### 1. 修改数据库配置
编辑 `server/config/database.js`：
```javascript
const dbConfig = {
  host: 'localhost',  // 宝塔MySQL默认localhost
  user: 'autojs_control',
  password: '你在宝塔设置的数据库密码',
  database: 'autojs_control',
  charset: 'utf8mb4',
  timezone: '+08:00'
};
```

#### 2. 修改前端配置
编辑 `web/vue.config.js`：
```javascript
proxy: {
  '/api': {
    target: 'http://你的服务器IP:3002',
    changeOrigin: true,
    ws: true
  },
  '/socket.io': {
    target: 'http://你的服务器IP:3002',
    changeOrigin: true,
    ws: true
  }
}
```

#### 3. 修改设备端配置
编辑 `scripts/双向.js`：
```javascript
var serverUrl = "http://你的服务器IP:3002";
```

## 🏗️ 第六步：构建和部署前端

### 安装前端依赖并构建
```bash
# 进入前端目录
cd /www/wwwroot/autojs-control/web

# 安装依赖
npm install

# 构建生产版本
npm run build
```

### 配置Nginx反向代理
1. **编辑网站配置**
   - 宝塔面板 → 网站 → 设置 → 配置文件

2. **添加反向代理配置**
```nginx
server {
    listen 80;
    server_name 你的域名或IP;
    
    # 前端静态文件
    location / {
        root /www/wwwroot/autojs-control/web/dist;
        index index.html;
        try_files $uri $uri/ /index.html;
    }
    
    # API反向代理
    location /api/ {
        proxy_pass http://127.0.0.1:3002;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
    
    # WebSocket反向代理
    location /socket.io/ {
        proxy_pass http://127.0.0.1:3002;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

3. **重载Nginx配置**
   - 宝塔面板 → 软件商店 → Nginx → 重载配置

## 🚀 第七步：启动后端服务

### 使用宝塔PM2管理器
1. **进入PM2管理器**
   - 宝塔面板 → 软件商店 → PM2管理器 → 管理

2. **添加Node.js项目**
   - 点击"添加项目"
   - 项目名称：`autojs-control`
   - 启动文件：`/www/wwwroot/autojs-control/server-main.js`
   - 项目目录：`/www/wwwroot/autojs-control`
   - Node版本：选择已安装的Node.js版本

3. **配置环境变量**
   - 在项目设置中添加环境变量：
   ```
   NODE_ENV=production
   SERVER_HOST=你的服务器IP
   DB_HOST=localhost
   DB_PASSWORD=你的数据库密码
   ```

4. **启动项目**
   - 点击"启动"按钮
   - 查看日志确认启动成功

### 手动PM2启动（备选方案）
```bash
# 进入项目目录
cd /www/wwwroot/autojs-control

# 安装依赖
npm install --production

# 创建PM2配置文件
cat > ecosystem.config.js << EOF
module.exports = {
  apps: [{
    name: 'autojs-control',
    script: 'server-main.js',
    cwd: '/www/wwwroot/autojs-control',
    instances: 1,
    env_production: {
      NODE_ENV: 'production',
      SERVER_HOST: '你的服务器IP',
      DB_HOST: 'localhost',
      DB_PASSWORD: '你的数据库密码'
    }
  }]
};
EOF

# 启动服务
pm2 start ecosystem.config.js --env production
pm2 save
```

## 🔍 第八步：验证部署

### 检查服务状态
1. **在宝塔PM2管理器中查看**
   - 项目状态应为"运行中"
   - CPU和内存使用正常

2. **检查端口监听**
   - 宝塔面板 → 系统安全 → 系统状态
   - 确认3002端口被监听

3. **测试网站访问**
   - 访问 `http://你的服务器IP`
   - 应该能看到登录页面

4. **测试API接口**
   - 访问 `http://你的服务器IP:3002/api/devices`
   - 应该返回JSON数据

### 功能测试
1. **用户登录测试**
   - 使用默认管理员账户登录
   - 用户名：admin，密码：admin123

2. **设备连接测试**
   - 运行修改后的设备端脚本
   - 在设备管理页面查看连接状态

3. **WebSocket测试**
   - 查看实时状态更新是否正常

## 🔒 第九步：安全配置

### SSL证书配置
1. **申请SSL证书**
   - 宝塔面板 → 网站 → 设置 → SSL
   - 选择"Let's Encrypt"免费证书
   - 或上传自有证书

2. **强制HTTPS**
   - 开启"强制HTTPS"选项
   - 配置HSTS

### 安全加固
1. **修改宝塔默认端口**
   - 宝塔面板 → 面板设置 → 面板端口
   - 改为非8888端口

2. **设置访问限制**
   - 宝塔面板 → 安全 → 面板设置
   - 设置授权IP访问

3. **数据库安全**
   - 修改MySQL默认端口
   - 禁用root远程登录

## 📊 第十步：监控和维护

### 宝塔监控配置
1. **系统监控**
   - 宝塔面板 → 监控
   - 开启系统资源监控

2. **网站监控**
   - 设置网站可用性监控
   - 配置告警通知

3. **日志管理**
   - 定期清理访问日志
   - 设置日志轮转

### 备份策略
1. **数据库备份**
   - 宝塔面板 → 计划任务
   - 设置数据库定时备份

2. **网站备份**
   - 设置网站文件定时备份
   - 配置远程备份存储

## 🚨 故障排除

### 常见问题解决

#### Node.js服务启动失败
1. 检查Node.js版本是否正确
2. 查看PM2日志：宝塔面板 → PM2管理器 → 查看日志
3. 检查端口是否被占用

#### 数据库连接失败
1. 检查MySQL服务状态
2. 验证数据库用户权限
3. 确认防火墙设置

#### 前端页面无法访问
1. 检查Nginx配置是否正确
2. 确认网站目录权限
3. 查看Nginx错误日志

#### WebSocket连接失败
1. 检查反向代理配置
2. 确认防火墙开放WebSocket端口
3. 验证Nginx支持WebSocket

### 日志查看位置
- **PM2日志**：宝塔面板 → PM2管理器 → 日志
- **Nginx日志**：`/www/wwwlogs/你的域名.log`
- **MySQL日志**：宝塔面板 → 数据库 → MySQL → 性能调优
- **系统日志**：宝塔面板 → 系统安全 → 系统日志

## 🎉 部署完成检查清单

- [ ] 宝塔面板安装完成
- [ ] 必要软件安装完成（Nginx, MySQL, PM2, Node.js）
- [ ] 数据库创建并导入表结构
- [ ] 网站创建并配置完成
- [ ] 项目代码上传完成
- [ ] 配置文件修改完成
- [ ] 前端构建并部署完成
- [ ] Nginx反向代理配置完成
- [ ] 后端服务启动成功
- [ ] SSL证书配置完成（可选）
- [ ] 功能测试通过
- [ ] 监控和备份配置完成

## 📞 技术支持

如果在宝塔部署过程中遇到问题：

1. **查看宝塔面板日志**：面板设置 → 面板日志
2. **检查软件运行状态**：软件商店 → 已安装
3. **查看系统资源使用**：监控 → 系统状态
4. **参考宝塔官方文档**：https://www.bt.cn/bbs/
5. **联系宝塔技术支持**：提交工单或社区求助

---

**🎯 使用宝塔面板，让部署更简单！**
