<template>
  <div class="xiaohongshu-logs">
    <el-card>
      <div slot="header" class="card-header">
        <span>小红书自动化执行日志</span>
        <div>
          <el-button
            type="primary"
            icon="el-icon-refresh"
            @click="refreshLogs"
            :loading="loading"
          >
            刷新
          </el-button>
          <el-button
            type="danger"
            icon="el-icon-delete"
            @click="clearLogs"
          >
            清空日志
          </el-button>
        </div>
      </div>

      <!-- 筛选条件 -->
      <div class="filter-section">
        <el-form :model="filterForm" inline>
          <el-form-item label="功能类型">
            <el-select v-model="filterForm.functionType" placeholder="选择功能类型" clearable>
              <el-option label="修改资料" value="profile"></el-option>
              <el-option label="搜索加群" value="groupChat"></el-option>
              <el-option label="循环群发" value="groupMessage"></el-option>
              <el-option label="文章评论" value="articleComment"></el-option>
              <el-option label="UID私信" value="uidMessage"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="设备">
            <el-select v-model="filterForm.deviceId" placeholder="选择设备" clearable>
              <el-option
                v-for="device in devices"
                :key="device.device_id"
                :label="device.device_name"
                :value="device.device_id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="执行状态">
            <el-select v-model="filterForm.executionStatus" placeholder="选择状态" clearable>
              <el-option label="等待中" value="pending"></el-option>
              <el-option label="执行中" value="running"></el-option>
              <el-option label="已完成" value="completed"></el-option>
              <el-option label="已失败" value="failed"></el-option>
              <el-option label="已停止" value="stopped"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="时间范围">
            <el-date-picker
              v-model="dateRange"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="yyyy-MM-dd HH:mm:ss"
              value-format="yyyy-MM-dd HH:mm:ss"
            ></el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="searchLogs">搜索</el-button>
            <el-button @click="resetFilter">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 日志表格 -->
      <el-table
        :data="logs"
        v-loading="loading"
        stripe
        style="width: 100%"
      >
        <el-table-column prop="task_id" label="任务ID" width="200" show-overflow-tooltip></el-table-column>
        <el-table-column prop="function_type" label="功能类型" width="120">
          <template slot-scope="scope">
            <el-tag :type="getFunctionTypeColor(scope.row.function_type)">
              {{ getFunctionTypeName(scope.row.function_type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="device_name" label="设备名称" width="150" show-overflow-tooltip></el-table-column>
        <el-table-column prop="selected_app" label="小红书应用" width="120" show-overflow-tooltip>
          <template slot-scope="scope">
            <span v-if="scope.row.selected_app && scope.row.selected_app !== '默认'">
              {{ scope.row.selected_app }}
            </span>
            <span v-else style="color: #909399;">默认</span>
          </template>
        </el-table-column>
        <el-table-column prop="execution_status" label="执行状态" width="100">
          <template slot-scope="scope">
            <el-tag :type="getStatusColor(scope.row.execution_status)">
              {{ getStatusName(scope.row.execution_status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="progress_percentage" label="进度" width="100">
          <template slot-scope="scope">
            <el-progress
              :percentage="scope.row.progress_percentage || 0"
              :status="getProgressStatus(scope.row.execution_status)"
              :stroke-width="8"
            ></el-progress>
          </template>
        </el-table-column>
        <el-table-column prop="started_at" label="开始时间" width="180">
          <template slot-scope="scope">
            {{ $moment(scope.row.started_at).format('YYYY-MM-DD HH:mm:ss') }}
          </template>
        </el-table-column>
        <el-table-column prop="execution_duration" label="执行时长" width="100">
          <template slot-scope="scope">
            {{ formatDuration(scope.row.execution_duration) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template slot-scope="scope">
            <!-- 停止按钮 - 只在执行中时显示 -->
            <el-button
              v-if="scope.row.execution_status === 'running'"
              type="text"
              size="small"
              style="color: #f56c6c;"
              @click="stopExecution(scope.row)"
              :loading="scope.row.stopping"
            >
              停止
            </el-button>

            <!-- 再来一次按钮 - 在完成、失败、停止时显示 -->
            <el-button
              v-if="['completed', 'failed', 'stopped'].includes(scope.row.execution_status)"
              type="text"
              size="small"
              style="color: #409eff;"
              @click="retryExecution(scope.row)"
            >
              再来一次
            </el-button>

            <el-button
              type="text"
              size="small"
              @click="viewLogDetail(scope.row)"
            >
              查看详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handlePageChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
        ></el-pagination>
      </div>
    </el-card>

    <!-- 日志详情对话框 -->
    <el-dialog
      title="执行日志详情"
      :visible.sync="detailDialogVisible"
      width="80%"
      :close-on-click-modal="false"
    >
      <div v-if="currentLog" class="log-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="任务ID">{{ currentLog.task_id }}</el-descriptions-item>
          <el-descriptions-item label="功能类型">
            <el-tag :type="getFunctionTypeColor(currentLog.function_type)">
              {{ getFunctionTypeName(currentLog.function_type) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="设备名称">{{ currentLog.device_name }}</el-descriptions-item>
          <el-descriptions-item label="设备ID">{{ currentLog.device_id }}</el-descriptions-item>
          <el-descriptions-item label="小红书应用">
            <span v-if="currentLog.selected_app && currentLog.selected_app !== '默认'">
              {{ currentLog.selected_app }}
            </span>
            <span v-else style="color: #909399;">默认</span>
          </el-descriptions-item>
          <el-descriptions-item label="执行状态">
            <el-tag :type="getStatusColor(currentLog.execution_status)">
              {{ getStatusName(currentLog.execution_status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="进度">{{ currentLog.progress_percentage || 0 }}%</el-descriptions-item>
          <el-descriptions-item label="开始时间">
            {{ $moment(currentLog.started_at).format('YYYY-MM-DD HH:mm:ss') }}
          </el-descriptions-item>
          <el-descriptions-item label="完成时间">
            {{ currentLog.completed_at ? $moment(currentLog.completed_at).format('YYYY-MM-DD HH:mm:ss') : '未完成' }}
          </el-descriptions-item>
          <el-descriptions-item label="执行时长">
            {{ formatDuration(currentLog.execution_duration) }}
          </el-descriptions-item>
        </el-descriptions>

        <div class="config-section">
          <h4>配置参数</h4>
          <pre class="config-content">{{ JSON.stringify(currentLog.config_params, null, 2) }}</pre>
        </div>

        <div v-if="currentLog.schedule_config" class="config-section">
          <h4>调度配置</h4>
          <pre class="config-content">{{ JSON.stringify(currentLog.schedule_config, null, 2) }}</pre>
        </div>

        <div v-if="currentLog.execution_result" class="config-section">
          <h4>执行结果</h4>
          <pre class="config-content">{{ JSON.stringify(currentLog.execution_result, null, 2) }}</pre>
        </div>

        <div v-if="currentLog.execution_logs" class="config-section">
          <h4>执行日志</h4>
          <div class="log-content">
            <pre>{{ currentLog.execution_logs }}</pre>
          </div>
        </div>

        <div v-if="currentLog.error_message" class="config-section">
          <h4>错误信息</h4>
          <el-alert
            :title="currentLog.error_message"
            type="error"
            :closable="false"
            show-icon
          ></el-alert>
        </div>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="detailDialogVisible = false">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'XiaohongshuLogs',
  data() {
    return {
      logs: [],
      loading: false,
      detailDialogVisible: false,
      currentLog: null,
      currentPage: 1,
      pageSize: 20,
      total: 0,
      dateRange: [],
      filterForm: {
        functionType: '',
        deviceId: '',
        executionStatus: ''
      },
      // 自动刷新相关
      refreshTimer: null,
      refreshInterval: 6000, // 6秒刷新一次
      isAutoRefreshEnabled: true,
      // 停止状态轮询相关
      stopPollingTimers: new Map() // 存储每个logId的轮询定时器
    }
  },
  computed: {
    devices() {
      return this.$store.getters['device/devices']
    }
  },
  async created() {
    await this.loadData()
    this.startAutoRefresh()
  },

  beforeDestroy() {
    this.stopAutoRefresh()
    this.clearAllStopPolling()
  },
  methods: {
    async loadData() {
      await this.$store.dispatch('device/fetchDevices')
      await this.loadLogs()
    },

    async loadLogs() {
      this.loading = true
      try {
        const params = {
          page: this.currentPage,
          limit: this.pageSize,
          ...this.filterForm
        }

        if (this.dateRange && this.dateRange.length === 2) {
          params.startDate = this.dateRange[0]
          params.endDate = this.dateRange[1]
        }

        const response = await this.$http.get('/api/xiaohongshu/logs', { params })

        if (response.data.success) {
          this.logs = response.data.data.logs || []
          this.total = response.data.data.total || 0
        } else {
          this.$message.error('加载日志失败: ' + response.data.message)
        }
      } catch (error) {
        console.error('加载小红书执行日志失败:', error)
        this.$message.error('加载日志失败')
      } finally {
        this.loading = false
      }
    },

    async refreshLogs() {
      await this.loadLogs()
    },

    // ===== 自动刷新方法 =====

    // 开始自动刷新
    startAutoRefresh() {
      if (!this.isAutoRefreshEnabled) return

      console.log(`[XiaohongshuLogs] 开始自动刷新，间隔: ${this.refreshInterval}ms`)
      this.refreshTimer = setInterval(() => {
        this.autoRefreshLogs()
      }, this.refreshInterval)
    },

    // 停止自动刷新
    stopAutoRefresh() {
      if (this.refreshTimer) {
        console.log('[XiaohongshuLogs] 停止自动刷新')
        clearInterval(this.refreshTimer)
        this.refreshTimer = null
      }
    },

    // 自动刷新日志（静默刷新）
    async autoRefreshLogs() {
      try {
        // 静默刷新，不显示loading状态
        const params = {
          page: this.currentPage,
          limit: this.pageSize,
          ...this.filterForm
        }

        if (this.dateRange && this.dateRange.length === 2) {
          params.startDate = this.dateRange[0]
          params.endDate = this.dateRange[1]
        }

        const response = await this.$http.get('/api/xiaohongshu/logs', { params })

        if (response.data.success) {
          const oldLogs = [...this.logs]
          this.logs = response.data.data.logs || []
          this.total = response.data.data.total || 0

          // 检查是否有状态变化，特别是stopped状态
          this.checkForStatusChanges(oldLogs, this.logs)

          // 只在控制台输出，不显示用户提示
          console.log('[XiaohongshuLogs] 执行日志自动刷新完成，当前日志数量:', this.logs.length)
        }
      } catch (error) {
        console.error('[XiaohongshuLogs] 自动刷新失败:', error)
        // 刷新失败时不显示错误提示，避免干扰用户
      }
    },

    // 检查状态变化
    checkForStatusChanges(oldLogs, newLogs) {
      try {
        newLogs.forEach(newLog => {
          const oldLog = oldLogs.find(old => old.log_id === newLog.log_id)

          // 如果状态从非stopped变为stopped，触发通知
          if (oldLog && oldLog.execution_status !== 'stopped' && newLog.execution_status === 'stopped') {
            console.log(`[状态变化检测] 检测到状态变为stopped: ${newLog.log_id}`)
            console.log(`[状态变化检测] 旧状态: ${oldLog.execution_status}, 新状态: ${newLog.execution_status}`)

            // 清除对应的停止轮询
            this.clearStopPolling(newLog.log_id)

            // 通知小红书自动化页面重置脚本状态
            this.notifyScriptStopped(newLog)

            // 显示成功提示
            this.$message.success(`脚本 ${newLog.function_name} 已成功停止`)
          }
        })
      } catch (error) {
        console.error('[状态变化检测] 检测失败:', error)
      }
    },

    // ===== 停止状态轮询方法 =====

    // 开始停止状态轮询
    startStopStatusPolling(logId) {
      console.log(`[停止轮询] 开始轮询停止状态: ${logId}`)

      // 清除可能存在的旧定时器
      this.clearStopPolling(logId)

      let pollCount = 0
      const maxPolls = 20 // 最多轮询20次（约1分钟）

      const timer = setInterval(async () => {
        pollCount++
        console.log(`[停止轮询] 第${pollCount}次轮询: ${logId}`)

        try {
          // 刷新日志列表
          await this.loadLogs()

          // 查找对应的日志记录
          const log = this.logs.find(l => l.log_id === logId)

          if (log) {
            console.log(`[停止轮询] 当前状态: ${log.execution_status}, 进度: ${log.progress_percentage}%`)

            // 如果状态已经变为stopped，停止轮询
            if (log.execution_status === 'stopped') {
              console.log(`[停止轮询] 停止完成: ${logId}`)
              this.clearStopPolling(logId)
              this.$message.success('脚本已成功停止')

              // 通知小红书自动化页面重置脚本状态
              this.notifyScriptStopped(log)

              return
            }
          } else {
            console.warn(`[停止轮询] 未找到日志记录: ${logId}`)
          }

          // 如果达到最大轮询次数，停止轮询
          if (pollCount >= maxPolls) {
            console.log(`[停止轮询] 达到最大轮询次数，停止轮询: ${logId}`)
            this.clearStopPolling(logId)
            this.$message.warning('停止状态检查超时，请手动刷新查看最新状态')
          }
        } catch (error) {
          console.error(`[停止轮询] 轮询失败: ${logId}`, error)
          // 轮询失败不停止，继续尝试
        }
      }, 2000) // 改为每2秒轮询一次，更快检测状态变化

      // 存储定时器
      this.stopPollingTimers.set(logId, timer)
    },

    // 清除指定logId的停止轮询
    clearStopPolling(logId) {
      const timer = this.stopPollingTimers.get(logId)
      if (timer) {
        clearInterval(timer)
        this.stopPollingTimers.delete(logId)
        console.log(`[停止轮询] 已清除轮询定时器: ${logId}`)
      }
    },

    // 清除所有停止轮询
    clearAllStopPolling() {
      console.log(`[停止轮询] 清除所有轮询定时器，数量: ${this.stopPollingTimers.size}`)
      for (const [logId, timer] of this.stopPollingTimers) {
        clearInterval(timer)
        console.log(`[停止轮询] 已清除: ${logId}`)
      }
      this.stopPollingTimers.clear()
    },

    // ===== 执行控制方法 =====

    // 停止执行
    async stopExecution(log) {
      try {
        console.log('[停止执行] 开始停止脚本:', log.log_id)

        // 设置停止状态，显示loading
        this.$set(log, 'stopping', true)

        const response = await this.$http.post('/api/xiaohongshu/stop', {
          taskId: log.task_id,
          deviceId: log.device_id,
          logId: log.log_id
        })

        if (response.data.success) {
          this.$message.success('停止命令已发送')
          console.log('[停止执行] 停止命令发送成功:', log.log_id)

          // 立即更新本地状态
          log.execution_status = 'stopping'
          log.status_message = '正在停止...'
          log.progress_percentage = 50

          // 立即通知小红书自动化页面重置脚本状态
          this.notifyScriptStopped(log)

          // 定期刷新日志列表，直到状态变为stopped
          this.startStopStatusPolling(log.log_id)
        } else {
          console.error('[停止执行] 停止命令发送失败:', response.data.message)
          this.$message.error(response.data.message || '停止失败')
        }
      } catch (error) {
        console.error('[停止执行] 停止执行失败:', error)
        this.$message.error('停止失败: ' + (error.response?.data?.message || error.message))
      } finally {
        this.$set(log, 'stopping', false)
      }
    },

    // 通知小红书自动化页面脚本已停止
    notifyScriptStopped(log) {
      try {
        console.log('[执行日志] 通知脚本停止:', log)

        // 根据功能名称确定功能类型
        const functionTypeMap = {
          '修改资料': 'profile',
          '搜索加群': 'searchGroupChat',
          '循环群发': 'groupMessage',
          '文章评论': 'articleComment',
          '手动输入UID私信': 'uidMessage',
          '文件上传UID私信': 'uidFileMessage',
          '发布视频': 'videoPublish',
          'UID私信': 'uidMessage' // 保持向后兼容
        }

        const functionType = functionTypeMap[log.function_name]

        if (functionType) {
          console.log(`[执行日志] 发送停止事件: ${functionType}`)

          // 发送全局事件通知小红书自动化页面
          this.$root.$emit('xiaohongshu-task-stopped', {
            functionType: functionType,
            reason: 'stopped_from_logs',
            logId: log.log_id,
            taskId: log.task_id,
            deviceId: log.device_id
          })

          // 同时更新Vuex状态
          this.$store.dispatch('xiaohongshu/stopTask', {
            functionType: functionType,
            reason: 'stopped_from_logs'
          })

          // 延迟一下再发送一次，确保事件被正确接收
          setTimeout(() => {
            console.log(`[执行日志] 延迟发送停止事件: ${functionType}`)
            this.$root.$emit('xiaohongshu-task-stopped', {
              functionType: functionType,
              reason: 'stopped_from_logs_delayed',
              logId: log.log_id,
              taskId: log.task_id,
              deviceId: log.device_id
            })
          }, 500)

          console.log(`[执行日志] 已通知 ${functionType} 功能重置状态`)
        } else {
          console.warn('[执行日志] 未知的功能名称:', log.function_name)
        }
      } catch (error) {
        console.error('[执行日志] 通知脚本停止失败:', error)
      }
    },

    // 重试执行（再来一次）
    async retryExecution(log) {
      try {
        // 确认操作
        const confirmed = await this.$confirm(
          `确定要重新执行"${this.getFunctionTypeName(log.function_type)}"功能吗？`,
          '确认重试',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )

        if (confirmed) {
          // 跳转到小红书自动化页面，并传递参数
          const routeData = {
            name: 'XiaohongshuAutomation',
            query: {
              retry: 'true',
              functionType: log.function_type,
              deviceId: log.device_id,
              originalTaskId: log.task_id
            }
          }

          // 如果有配置参数，也传递过去
          if (log.config_params) {
            try {
              const config = JSON.parse(log.config_params)
              routeData.query.config = JSON.stringify(config)
            } catch (e) {
              console.warn('解析配置参数失败:', e)
            }
          }

          this.$router.push(routeData)
          this.$message.info('正在跳转到小红书自动化页面...')
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('重试执行失败:', error)
          this.$message.error('操作失败')
        }
      }
    },

    async clearLogs() {
      try {
        await this.$confirm('确定要清空所有小红书执行日志吗？此操作不可恢复！', '确认清空', {
          type: 'warning'
        })

        const response = await this.$http.delete('/api/xiaohongshu/logs')

        if (response.data.success) {
          this.$message.success(response.data.message)
          await this.loadLogs()
        } else {
          this.$message.error('清空失败: ' + response.data.message)
        }
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error('清空失败: ' + error.message)
        }
      }
    },

    searchLogs() {
      this.currentPage = 1
      this.loadLogs()
    },

    resetFilter() {
      this.filterForm = {
        functionType: '',
        deviceId: '',
        executionStatus: ''
      }
      this.dateRange = []
      this.currentPage = 1
      this.loadLogs()
    },

    handlePageChange(page) {
      this.currentPage = page
      this.loadLogs()
    },

    handleSizeChange(size) {
      this.pageSize = size
      this.currentPage = 1
      this.loadLogs()
    },

    viewLogDetail(log) {
      this.currentLog = log
      this.detailDialogVisible = true
    },

    getFunctionTypeName(type) {
      const nameMap = {
        'profile': '修改资料',
        'searchGroupChat': '搜索加群',
        'groupMessage': '循环群发',
        'articleComment': '文章评论',
        'uidMessage': '手动输入UID私信',
        'uidFileMessage': '文件上传UID私信',
        'videoPublish': '发布视频'
      }
      return nameMap[type] || type
    },

    getFunctionTypeColor(type) {
      const colorMap = {
        'profile': 'primary',
        'searchGroupChat': 'success',
        'groupMessage': 'warning',
        'articleComment': 'info',
        'uidMessage': 'danger',
        'uidFileMessage': 'warning'
      }
      return colorMap[type] || ''
    },

    getStatusName(status) {
      const nameMap = {
        'pending': '等待中',
        'running': '执行中',
        'completed': '已完成',
        'failed': '已失败',
        'stopped': '已停止'
      }
      return nameMap[status] || status
    },

    getStatusColor(status) {
      const colorMap = {
        'pending': 'info',
        'running': 'warning',
        'completed': 'success',
        'failed': 'danger',
        'stopped': 'info'
      }
      return colorMap[status] || ''
    },

    getProgressStatus(status) {
      if (status === 'completed') return 'success'
      if (status === 'failed') return 'exception'
      return null
    },

    formatDuration(seconds) {
      if (!seconds) return '0秒'

      const hours = Math.floor(seconds / 3600)
      const minutes = Math.floor((seconds % 3600) / 60)
      const secs = seconds % 60

      if (hours > 0) {
        return `${hours}小时${minutes}分${secs}秒`
      } else if (minutes > 0) {
        return `${minutes}分${secs}秒`
      } else {
        return `${secs}秒`
      }
    }
  }
}
</script>

<style scoped>
.xiaohongshu-logs {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.filter-section {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.pagination-wrapper {
  margin-top: 20px;
  text-align: center;
}

.log-detail {
  max-height: 70vh;
  overflow-y: auto;
}

.config-section {
  margin-top: 20px;
}

.config-section h4 {
  margin-bottom: 10px;
  color: #303133;
  font-weight: 600;
}

.config-content {
  background-color: #f5f7fa;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 15px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.5;
  max-height: 300px;
  overflow-y: auto;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.log-content {
  background-color: #2d3748;
  color: #e2e8f0;
  border-radius: 4px;
  padding: 15px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.5;
  max-height: 400px;
  overflow-y: auto;
}

.log-content pre {
  margin: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.dialog-footer {
  text-align: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .xiaohongshu-logs {
    padding: 10px;
  }

  .filter-section {
    padding: 15px;
  }

  .config-content,
  .log-content {
    font-size: 11px;
    padding: 10px;
  }
}
</style>
