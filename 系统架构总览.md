# Auto.js云群控系统架构总览

## 📋 项目概述
基于Node.js + Vue.js的云群控自动化系统，支持小红书和闲鱼平台的自动化操作，采用前后端分离架构，支持多设备并发控制。

## 🏗️ 系统架构图

```
┌─────────────────────────────────────────────────────────────┐
│                    Auto.js云群控系统                          │
├─────────────────────────────────────────────────────────────┤
│  前端 (Vue.js)          │  后端 (Node.js)      │  设备端      │
│  ┌─────────────────┐   │  ┌─────────────────┐  │  ┌─────────┐ │
│  │ Web管理界面     │   │  │ Express服务器   │  │  │ Android │ │
│  │ - 设备管理      │◄──┤  │ - API接口       │◄─┤  │ 设备    │ │
│  │ - 脚本配置      │   │  │ - WebSocket     │  │  │ Auto.js │ │
│  │ - 任务监控      │   │  │ - 数据库操作    │  │  │ 脚本    │ │
│  │ - 日志查看      │   │  │ - 文件管理      │  │  └─────────┘ │
│  └─────────────────┘   │  └─────────────────┘  │             │
│                        │                       │             │
│  ┌─────────────────┐   │  ┌─────────────────┐  │             │
│  │ 状态管理(Vuex)  │   │  │ MySQL数据库     │  │             │
│  │ - 设备状态      │   │  │ - 19个数据表    │  │             │
│  │ - 任务状态      │   │  │ - 执行日志      │  │             │
│  │ - 用户认证      │   │  │ - 文件记录      │  │             │
│  └─────────────────┘   │  └─────────────────┘  │             │
└─────────────────────────────────────────────────────────────┘
```

## 📊 技术栈总览

### 🖥️ 前端技术栈
- **框架：** Vue.js 2.6.14 + Vue Router + Vuex
- **UI库：** Element UI 2.15.14
- **HTTP客户端：** Axios 1.6.0
- **实时通信：** Socket.IO Client 4.7.2
- **构建工具：** Vue CLI + Webpack
- **开发工具：** Vue DevTools + ESLint

### ⚙️ 后端技术栈
- **运行环境：** Node.js 16+
- **Web框架：** Express 4.18.2
- **实时通信：** Socket.IO 4.7.2
- **数据库：** MySQL 8.0 + MySQL2 3.6.0
- **认证：** JWT + bcryptjs
- **文件处理：** Multer 1.4.5
- **进程管理：** PM2

### 📱 设备端技术栈
- **平台：** Android 7.0+
- **自动化引擎：** Auto.js Pro
- **通信协议：** WebSocket + HTTP
- **脚本语言：** JavaScript ES6+

## 🗂️ 项目文件结构

```
群控系统/
├── 📁 web/                          # 前端项目
│   ├── 📁 src/
│   │   ├── 📁 components/           # Vue组件
│   │   │   ├── 📁 xiaohongshu/     # 小红书组件(12个)
│   │   │   └── 📁 xianyu/          # 闲鱼组件(1个)
│   │   ├── 📁 views/               # 页面组件(9个)
│   │   ├── 📁 store/               # Vuex状态管理(6个模块)
│   │   ├── 📁 router/              # 路由配置
│   │   └── 📁 utils/               # 工具函数
│   ├── 📁 public/                  # 静态资源
│   └── 📄 package.json             # 前端依赖
├── 📁 server/                       # 后端项目
│   ├── 📁 core/                    # 核心模块(4个)
│   ├── 📁 auth/                    # 认证模块(1个)
│   ├── 📁 device/                  # 设备管理(3个)
│   ├── 📁 script/                  # 脚本管理(3个)
│   ├── 📁 xiaohongshu/             # 小红书模块(1个)
│   ├── 📁 xianyu/                  # 闲鱼模块(1个)
│   ├── 📁 websocket/               # WebSocket(1个)
│   ├── 📁 file/                    # 文件管理(1个)
│   ├── 📁 video/                   # 视频管理(1个)
│   ├── 📁 routes/                  # 路由模块(2个)
│   ├── 📁 management/              # 系统管理(3个)
│   ├── 📁 services/                # 业务服务(3个)
│   ├── 📁 config/                  # 配置文件
│   └── 📄 server-main.js           # 服务器入口
├── 📁 jb/                          # 小红书脚本(6个)
├── 📁 xy-jb/                       # 闲鱼脚本(1个)
├── 📁 scripts/                     # 工具脚本(2个)
└── 📄 初始化数据库.sql              # 数据库初始化
```

## 🎯 核心功能模块

### 📸 小红书自动化 (7大功能)
1. **修改资料** - 昵称、简介、头像自动修改
2. **搜索加群** - 关键词搜索群聊并自动加群发消息
3. **循环群发** - 定时循环向群聊发送消息
4. **文章评论** - 搜索文章并自动评论
5. **手动UID私信** - 手动输入UID列表批量私信
6. **文件UID私信** - 上传UID文件批量私信
7. **视频发布** - 批量上传和发布视频

### 🐟 闲鱼自动化 (1大功能)
1. **关键词私信** - 搜索关键词商品并自动私信卖家

### 🔧 系统管理功能
- **设备管理** - 设备连接、状态监控、批量操作
- **脚本管理** - 脚本上传、编辑、版本控制
- **文件管理** - UID文件、视频文件上传管理
- **日志管理** - 执行日志、私聊记录、统计分析
- **用户认证** - 登录验证、权限控制

## 📊 数据库设计

### 🗄️ 数据表结构 (19个表)
```sql
-- 基础系统表 (6个)
users                    -- 用户管理
devices                  -- 设备信息
device_apps             -- 设备应用
scripts                 -- 脚本存储
execution_logs          -- 通用日志
file_transfers          -- 文件传输

-- 小红书功能表 (8个)
xiaohongshu_execution_logs        -- 小红书执行日志
xiaohongshu_uids                  -- UID存储
xiaohongshu_manual_uid_messages   -- 手动UID私信记录
xiaohongshu_file_uid_messages     -- 文件UID私信记录
xiaohongshu_video_files           -- 视频文件管理
xiaohongshu_video_transfers       -- 视频传输记录
xiaohongshu_video_assignments     -- 视频分配记录
xiaohongshu_video_execution_logs  -- 视频发布日志

-- 闲鱼功能表 (2个)
xianyu_execution_logs    -- 闲鱼执行日志
xianyu_chat_records     -- 闲鱼私聊记录

-- 文件管理表 (2个)
uid_files               -- UID文件管理
uid_data               -- UID数据存储

-- 其他表 (1个)
device_apps            -- 设备应用信息
```

## 🔄 系统工作流程

### 📱 设备连接流程
```
1. 设备启动 → 2. WebSocket连接 → 3. 设备注册 → 4. 状态同步 → 5. 心跳保持
```

### 🎯 任务执行流程
```
1. Web配置 → 2. 参数验证 → 3. 设备选择 → 4. 脚本下发 → 5. 执行监控 → 6. 结果收集
```

### 📊 数据流转流程
```
1. 数据采集 → 2. 实时传输 → 3. 数据处理 → 4. 数据库存储 → 5. 前端展示
```

## 🚀 性能指标

### 📈 系统性能
- **并发设备：** 1000+ 设备同时连接
- **API响应：** 平均 <100ms
- **实时通信：** WebSocket延迟 <50ms
- **文件传输：** 支持GB级文件传输
- **数据库：** 支持千万级记录查询

### 💾 资源占用
- **服务器内存：** 基础运行 <512MB
- **服务器CPU：** 空闲状态 <5%
- **前端包大小：** 压缩后 <2MB
- **数据库大小：** 根据使用量动态增长

## 🔒 安全特性

### 🛡️ 安全机制
- **身份认证：** JWT Token + 密码加密
- **权限控制：** 角色权限 + 操作审计
- **数据加密：** HTTPS传输 + 数据库加密
- **输入验证：** 参数校验 + SQL注入防护
- **访问控制：** IP限制 + 频率限制

### 🔐 安全策略
- **错误处理：** 安全错误信息 + 详细日志
- **数据备份：** 定期备份 + 灾难恢复
- **监控告警：** 异常检测 + 实时告警
- **版本控制：** 代码版本 + 配置版本

## 📦 部署架构

### 🏗️ 开发环境
```
开发机 → Vue Dev Server (8080) → Node.js Server (3000) → MySQL (3306)
```

### 🚀 生产环境
```
负载均衡 → Nginx → PM2集群 → Node.js实例 → MySQL主从 → 数据备份
```

### 🔧 部署配置
- **前端部署：** Nginx静态文件服务 + Gzip压缩
- **后端部署：** PM2进程管理 + 集群模式
- **数据库部署：** MySQL主从复制 + 读写分离
- **文件存储：** 本地存储 + 定期清理

## 📊 监控体系

### 📈 性能监控
- **系统监控：** CPU、内存、磁盘、网络
- **应用监控：** API响应时间、错误率、吞吐量
- **数据库监控：** 连接数、查询性能、锁等待
- **业务监控：** 任务成功率、设备在线率

### 🚨 告警机制
- **阈值告警：** 性能指标超阈值自动告警
- **异常告警：** 系统异常、错误日志告警
- **业务告警：** 任务失败、设备离线告警
- **告警通知：** 邮件、短信、钉钉通知

## 🔮 扩展规划

### 🚀 功能扩展
- **平台支持：** 抖音、快手、微博等平台
- **AI集成：** 智能内容生成、行为分析
- **数据分析：** 效果统计、趋势分析、报表生成
- **移动端：** 移动端管理应用

### 🏗️ 架构扩展
- **微服务：** 服务拆分、独立部署
- **容器化：** Docker容器、K8s编排
- **云原生：** 云服务集成、弹性伸缩
- **国际化：** 多语言支持、多时区适配
