/**
 * 主服务器文件 - 模块化版本
 * 整合所有拆分的模块，提供完整的群控服务器功能
 *
 * 模块结构：
 * - server-core.js: 核心配置和基础服务 (247行)
 * - server-device.js: 设备管理相关API (2031行)
 * - server-script.js: 脚本管理相关API (1070行)
 * - server-xiaohongshu.js: 小红书自动化API (2640行)
 * - server-xianyu.js: 闲鱼自动化API (1381行)
 * - server-websocket.js: WebSocket连接管理 (234行)
 *
 * 总计：7603行 (原始文件13281行的57%)
 */

const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const cors = require('cors');
const path = require('path');

// 日志节流机制
const logThrottleMap = new Map();
const LOG_THROTTLE_INTERVAL = 5 * 60 * 1000; // 5分钟间隔

// 节流日志函数
function throttledLog(key, message) {
  const now = Date.now();
  const lastLogTime = logThrottleMap.get(key);

  if (!lastLogTime || (now - lastLogTime) >= LOG_THROTTLE_INTERVAL) {
    console.log(message);
    logThrottleMap.set(key, now);
    return true;
  }
  return false;
}

// 导入拆分的模块
const { setupServerCore } = require('./core/server-core');
const { setupServerDevice } = require('./device/server-device');
const { setupDeviceConnectionCodes } = require('./device/device-connection-codes');
const { userIsolationMiddleware } = require('./middleware/userIsolation');
const { setupServerScript } = require('./script/server-script');
const { setupServerXiaohongshu } = require('./xiaohongshu/server-xiaohongshu');
const { setupServerXianyu } = require('./xianyu/server-xianyu');
const { setupServerWebSocket } = require('./websocket/server-websocket');
const { setupServerFile } = require('./file/server-file');
const { setupServerVideo } = require('./video/server-video');
const { setupServerRoutes } = require('./routes_modules/server-routes');
const { setupServerDebug } = require('./debug/server-debug');
const { setupServerAuth } = require('./auth/server-auth');
const { setupServerDeviceApps } = require('./device/server-device-apps');
const { setupServerMonitor } = require('./management/server-monitor');
const { setupServerManagement } = require('./management/server-management');
const { setupServerShutdown } = require('./management/server-shutdown');
const { setupServerScriptGenerator } = require('./script/server-script-generator');
const { setupServerDeviceCommands } = require('./device/server-device-commands');
const { setupServerAdditionalApis } = require('./utils/server-additional-apis');
const { setupServerUtils } = require('./utils/server-utils');
const { setupServerErrorHandling } = require('./core/server-error-handling');
const { setupServerPerformance } = require('./core/server-performance');
const { setupServerPublicApis } = require('./utils/server-public-apis');
const { setupServerRouteHandlers } = require('./routes_modules/server-route-handlers');
const { setupServerVariables } = require('./core/server-variables');
const { setupServerHomepage } = require('./routes_modules/server-homepage');
const { setupAdminActivationCodes } = require('./admin/admin-activation-codes');
const { setupAdminUserManagement } = require('./admin/admin-user-management');

async function startServer() {
  console.log('🚀 启动模块化群控服务器...');

  try {
    // 创建Express应用
    const app = express();
    const server = http.createServer(app);
    const io = socketIo(server, {
      cors: {
        origin: "*",
        methods: ["GET", "POST"]
      }
    });

    // 配置CORS中间件 - 确保在所有路由之前
    app.use(cors({
      origin: function (origin, callback) {
        // 允许没有origin的请求（如移动应用、Postman等）
        if (!origin) return callback(null, true);

        // 允许localhost和本地IP地址的所有端口
        const allowedOrigins = [
          /^http:\/\/localhost:\d+$/,
          /^http:\/\/127\.0\.0\.1:\d+$/,
          /^http:\/\/192\.168\.\d+\.\d+:\d+$/,
          /^http:\/\/10\.\d+\.\d+\.\d+:\d+$/,
          /^http:\/\/172\.(1[6-9]|2\d|3[01])\.\d+\.\d+:\d+$/
        ];

        const isAllowed = allowedOrigins.some(pattern => pattern.test(origin));

        if (isAllowed) {
          callback(null, true);
        } else {
          console.log('CORS blocked origin:', origin);
          callback(new Error('Not allowed by CORS'));
        }
      },
      credentials: true,
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
      allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With', 'Accept', 'Origin'],
      exposedHeaders: ['Content-Range', 'X-Content-Range']
    }));

    // 添加强制CORS中间件，确保所有响应都包含CORS头
    app.use((req, res, next) => {
      // 强制设置CORS头
      res.header('Access-Control-Allow-Origin', req.headers.origin || '*');
      res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS, PATCH, HEAD');
      res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With, Accept, Origin');
      res.header('Access-Control-Allow-Credentials', 'true');
      res.header('Access-Control-Max-Age', '86400');

      // 使用节流日志，避免频繁输出CORS信息
      const corsLogKey = `cors_${req.method}_${req.url.split('?')[0]}`;
      throttledLog(corsLogKey, `🔧 [CORS] ${req.method} ${req.url} - Origin: ${req.headers.origin || 'none'}`);

      // 如果是OPTIONS请求，直接返回200
      if (req.method === 'OPTIONS') {
        throttledLog(`${corsLogKey}_options`, '🔍 [CORS] 处理OPTIONS预检请求: ' + req.url);
        res.status(200).end();
        return;
      }

      next();
    });

    // 基础中间件
    app.use(express.json({ limit: '100mb' }));
    app.use(express.urlencoded({ extended: true, limit: '100mb' }));

    console.log('✅ Express应用和Socket.IO已初始化');

    // 0. 设置服务器变量模块
    console.log('📦 设置服务器变量模块...');
    const serverVariables = await setupServerVariables();
    console.log('✅ 服务器变量模块设置完成');

    // 1. 设置核心模块（数据库连接、认证等）
    console.log('📦 设置核心模块...');
    const { coreData, authData } = await setupServerCore(app, io);
    console.log('✅ 核心模块设置完成');

    // 2. 设置设备管理模块
    console.log('📦 设置设备管理模块...');
    const deviceFunctions = await setupServerDevice(app, io, coreData, authData);
    console.log('✅ 设备管理模块设置完成');

    // 2.5. 设置设备连接码管理模块
    console.log('📦 设置设备连接码管理模块...');
    try {
      await setupDeviceConnectionCodes(app, coreData.pool, authData.authenticateToken, userIsolationMiddleware);
      console.log('✅ 设备连接码管理模块设置完成');
    } catch (error) {
      console.log('⚠️ 设备连接码管理模块设置失败:', error.message);
    }

    // 3. 设置脚本管理模块
    console.log('📦 设置脚本管理模块...');
    const scriptFunctions = await setupServerScript(app, io, coreData, authData);
    console.log('✅ 脚本管理模块设置完成');

    // 4. 设置服务器工具函数模块（需要在小红书模块之前）
    console.log('📦 设置服务器工具函数模块...');
    const utilsFunctions = await setupServerUtils(app, io, coreData, authData);
    console.log('✅ 服务器工具函数模块设置完成');

    // 5. 设置小红书自动化模块
    console.log('📦 设置小红书自动化模块...');
    const xiaohongshuFunctions = await setupServerXiaohongshu(app, io, coreData, authData, utilsFunctions);
    console.log('✅ 小红书自动化模块设置完成');

    // 6. 设置闲鱼自动化模块
    console.log('📦 设置闲鱼自动化模块...');
    const xianyuFunctions = await setupServerXianyu(app, io, coreData, authData);
    console.log('✅ 闲鱼自动化模块设置完成');

    // 7. 设置WebSocket连接管理模块
    console.log('📦 设置WebSocket连接管理模块...');
    const websocketFunctions = await setupServerWebSocket(app, server, io, coreData, authData);
    console.log('✅ WebSocket连接管理模块设置完成');

    // 7. 设置文件管理模块
    console.log('📦 设置文件管理模块...');
    const fileFunctions = await setupServerFile(app, io, coreData, authData);
    console.log('✅ 文件管理模块设置完成');

    // 8. 设置视频管理模块
    console.log('📦 设置视频管理模块...');
    const videoFunctions = await setupServerVideo(app, io, coreData, authData);
    console.log('✅ 视频管理模块设置完成');

    // 8.5. 设置主页路由模块（必须在静态文件服务之前）
    console.log('📦 设置主页路由模块...');
    const homepageFunctions = await setupServerHomepage(app, io, coreData, authData);
    console.log('✅ 主页路由模块设置完成');

    // 9. 设置静态页面路由模块
    console.log('📦 设置静态页面路由模块...');
    const routeFunctions = await setupServerRoutes(app, io, coreData, authData);
    console.log('✅ 静态页面路由模块设置完成');

    // 10. 设置调试和测试模块
    console.log('📦 设置调试和测试模块...');
    const debugFunctions = await setupServerDebug(app, io, coreData, authData);
    console.log('✅ 调试和测试模块设置完成');

    // 11. 设置认证模块
    console.log('📦 设置认证模块...');
    const authFunctions = await setupServerAuth(app, io, coreData, authData);
    console.log('✅ 认证模块设置完成');

    // 12. 设置设备应用管理模块
    console.log('📦 设置设备应用管理模块...');
    const deviceAppsFunctions = await setupServerDeviceApps(app, io, coreData, authData);
    console.log('✅ 设备应用管理模块设置完成');

    // 13. 设置系统监控模块
    console.log('📦 设置系统监控模块...');
    const monitorFunctions = await setupServerMonitor(app, io, coreData, authData);
    console.log('✅ 系统监控模块设置完成');

    // 14. 设置服务器管理模块
    console.log('📦 设置服务器管理模块...');
    const managementFunctions = await setupServerManagement(app, server, io, coreData, authData);
    console.log('✅ 服务器管理模块设置完成');

    // 15. 设置服务器优雅关闭模块
    console.log('📦 设置服务器优雅关闭模块...');
    const shutdownFunctions = await setupServerShutdown(app, server, io, coreData, authData);
    console.log('✅ 服务器优雅关闭模块设置完成');

    // 16. 设置脚本生成模块
    console.log('📦 设置脚本生成模块...');
    const scriptGeneratorFunctions = await setupServerScriptGenerator(app, io, coreData, authData);
    console.log('✅ 脚本生成模块设置完成');

    // 17. 设置设备命令管理模块
    console.log('📦 设置设备命令管理模块...');
    const deviceCommandsFunctions = await setupServerDeviceCommands(app, io, coreData, authData);
    console.log('✅ 设备命令管理模块设置完成');

    // 18. 设置额外API模块
    console.log('📦 设置额外API模块...');
    const additionalApisFunctions = await setupServerAdditionalApis(app, io, coreData, authData);
    console.log('✅ 额外API模块设置完成');

    // 19. 服务器工具函数模块已在第4步设置

    // 20. 设置服务器错误处理模块
    console.log('📦 设置服务器错误处理模块...');
    const errorHandlingFunctions = await setupServerErrorHandling(app, io, coreData, authData);
    console.log('✅ 服务器错误处理模块设置完成');

    // 21. 设置服务器性能优化模块
    console.log('📦 设置服务器性能优化模块...');
    const performanceFunctions = await setupServerPerformance(app, io, coreData, authData);
    console.log('✅ 服务器性能优化模块设置完成');

    // 22. 设置服务器公共API模块
    console.log('📦 设置服务器公共API模块...');
    const publicApisFunctions = await setupServerPublicApis(app, io, coreData, authData);
    console.log('✅ 服务器公共API模块设置完成');

    // 23. 设置管理员卡密管理模块
    console.log('📦 设置管理员卡密管理模块...');
    const adminActivationCodesFunctions = await setupAdminActivationCodes(app, io, coreData, authData);
    console.log('✅ 管理员卡密管理模块设置完成');

    // 24. 设置管理员用户管理模块
    console.log('📦 设置管理员用户管理模块...');
    const adminUserManagementFunctions = await setupAdminUserManagement(app, io, coreData, authData);
    console.log('✅ 管理员用户管理模块设置完成');

    // 25. 设置服务器路由处理模块（必须放在最后）
    console.log('📦 设置服务器路由处理模块...');
    const routeHandlersFunctions = await setupServerRouteHandlers(app, io, coreData, authData);
    console.log('✅ 服务器路由处理模块设置完成');

    // 静态文件服务
    app.use(express.static(path.join(__dirname, '../public')));
    app.use('/uploads', express.static(path.join(__dirname, '../uploads')));

    // 配置缩略图静态文件服务
    app.use('/thumbnails', express.static(path.join(__dirname, '../uploads/thumbnails')));

    // 设置SVG缩略图的正确MIME类型
    app.use('/thumbnails', (req, res, next) => {
      if (req.path.endsWith('.svg')) {
        res.setHeader('Content-Type', 'image/svg+xml');
      }
      next();
    });

    // 根路由
    app.get('/', (req, res) => {
      res.json({
        success: true,
        message: '群控服务器运行正常',
        version: '2.0.0-modular',
        timestamp: new Date().toISOString(),
        modules: {
          core: '✅ 已加载',
          device: '✅ 已加载',
          script: '✅ 已加载',
          xiaohongshu: '✅ 已加载',
          xianyu: '✅ 已加载',
          websocket: '✅ 已加载',
          file: '✅ 已加载',
          video: '✅ 已加载',
          routes: '✅ 已加载',
          debug: '✅ 已加载',
          auth: '✅ 已加载',
          deviceApps: '✅ 已加载',
          monitor: '✅ 已加载',
          management: '✅ 已加载',
          shutdown: '✅ 已加载',
          scriptGenerator: '✅ 已加载',
          deviceCommands: '✅ 已加载',
          additionalApis: '✅ 已加载',
          utils: '✅ 已加载',
          errorHandling: '✅ 已加载',
          performance: '✅ 已加载',
          publicApis: '✅ 已加载',
          routeHandlers: '✅ 已加载',
          variables: '✅ 已加载'
        },
        statistics: {
          totalLines: 16500,
          originalLines: 13281,
          completionRate: '124%+'
        }
      });
    });

    // 健康检查路由已在server-management.js模块中定义，避免重复
    // 启动服务器
    const PORT = process.env.PORT || 3002;
    const HOST = '0.0.0.0'; // 强制监听所有网络接口

    console.log(`🔧 准备启动服务器，绑定到 ${HOST}:${PORT}`);

    server.listen(PORT, HOST, () => {
      console.log('🎉 ===== 模块化群控服务器启动成功 =====');
      console.log(`🌐 服务器地址: http://localhost:${PORT}`);
      console.log(`🌐 局域网地址: http://************:${PORT}`);
      console.log(`🌐 监听地址: ${HOST}:${PORT}`);
      console.log(`📊 已加载模块: 24个`);
      console.log(`📝 总代码行数: 约16,500行`);
      console.log(`📈 完成度: 124%+ (16,500/13,281)`);
      console.log('🔧 模块列表:');

      // 测试网络绑定
      console.log('🔍 测试网络绑定...');
      console.log(`✅ 服务器已绑定到 ${HOST}:${PORT}`);
      console.log('📡 可用访问地址:');
      console.log(`   - http://localhost:${PORT}`);
      console.log(`   - http://127.0.0.1:${PORT}`);
      console.log(`   - http://************:${PORT}`);

      console.log('🔧 模块列表:');
      console.log('  - server-variables.js (约300行): 服务器变量声明');
      console.log('  - server-core.js (247行): 核心配置和基础服务');
      console.log('  - server-device.js (2031行): 设备管理相关API');
      console.log('  - server-script.js (1070行): 脚本管理相关API');
      console.log('  - server-xiaohongshu.js (2640行): 小红书自动化API');
      console.log('  - server-xianyu.js (1381行): 闲鱼自动化API');
      console.log('  - server-websocket.js (234行): WebSocket连接管理');
      console.log('  - server-file.js (246行): 文件管理相关API');
      console.log('  - server-video.js (501行): 视频管理相关API');
      console.log('  - server-routes.js (236行): 静态页面路由');
      console.log('  - server-debug.js (529行): 调试和测试API');
      console.log('  - server-auth.js (95行): 认证相关API');
      console.log('  - server-device-apps.js (249行): 设备应用管理API');
      console.log('  - server-monitor.js (375行): 系统监控API');
      console.log('  - server-management.js (141行): 服务器管理功能');
      console.log('  - server-shutdown.js (352行): 优雅关闭处理');
      console.log('  - server-script-generator.js (259行): 脚本生成转换');
      console.log('  - server-device-commands.js (492行): 设备命令管理');
      console.log('  - server-additional-apis.js (200行): 额外API功能');
      console.log('  - server-utils.js (305行): 服务器工具函数');
      console.log('  - server-error-handling.js (250行): 错误处理模块');
      console.log('  - server-performance.js (300行): 性能优化模块');
      console.log('  - server-public-apis.js (约430行): 公共API模块');
      console.log('  - server-route-handlers.js (约120行): 路由处理模块');
      console.log('===============================================');
    });

    // 优雅关闭处理
    process.on('SIGTERM', () => {
      console.log('收到SIGTERM信号，正在关闭服务器...');
      server.close(() => {
        console.log('服务器已关闭');
        if (coreData.pool) {
          coreData.pool.end();
        }
        process.exit(0);
      });
    });

    process.on('SIGINT', () => {
      console.log('收到SIGINT信号，正在关闭服务器...');
      server.close(() => {
        console.log('服务器已关闭');
        if (coreData.pool) {
          coreData.pool.end();
        }
        process.exit(0);
      });
    });

  } catch (error) {
    console.error('❌ 服务器启动失败:', error);
    process.exit(1);
  }
}

// 启动服务器
if (require.main === module) {
  startServer();
}

module.exports = { startServer };
