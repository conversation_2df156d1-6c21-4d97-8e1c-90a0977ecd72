# Auto.js云群控系统远程服务器部署流程文档

## 📋 部署概述

本文档详细说明如何将Auto.js云群控系统从本地开发环境部署到远程服务器（Linux/Windows），包括数据库迁移、配置修改、服务启动等完整流程。

## 🎯 部署架构

### 当前本地架构
```
本地开发环境:
├── 前端: Vue Dev Server (localhost:8080)
├── 后端: Node.js Server (localhost:3002)
├── 数据库: MySQL (localhost:3306)
└── 设备端: Auto.js脚本 (连接localhost:3002)
```

### 目标远程架构
```
远程服务器环境:
├── 前端: Nginx静态文件服务 (公网IP:80/443)
├── 后端: PM2管理Node.js (公网IP:3002)
├── 数据库: MySQL (远程数据库服务器)
└── 设备端: Auto.js脚本 (连接公网IP:3002)
```

## 🔧 需要修改的配置文件

### 📁 配置文件清单
1. **数据库配置**: `server/config/database.js`
2. **前端代理配置**: `web/vue.config.js`
3. **前端服务器配置**: `web/src/utils/serverConfig.js`
4. **设备端连接配置**: `scripts/双向.js`
5. **后端服务器配置**: `server/core/server-core.js`

## 📝 统一配置文件方案

### 创建环境配置文件

首先创建一个统一的环境配置文件，方便管理所有服务器地址：

```javascript
// config/environment.js
module.exports = {
  // 开发环境配置
  development: {
    SERVER_HOST: 'localhost',
    SERVER_PORT: 3002,
    DATABASE_HOST: 'localhost',
    DATABASE_PORT: 3306,
    DATABASE_USER: 'autojs_control',
    DATABASE_PASSWORD: 'root',
    DATABASE_NAME: 'autojs_control',
    FRONTEND_HOST: 'localhost',
    FRONTEND_PORT: 8080
  },
  
  // 生产环境配置
  production: {
    SERVER_HOST: '你的服务器公网IP', // 需要替换
    SERVER_PORT: 3002,
    DATABASE_HOST: '你的数据库服务器IP', // 需要替换
    DATABASE_PORT: 3306,
    DATABASE_USER: 'autojs_control',
    DATABASE_PASSWORD: '你的数据库密码', // 需要替换
    DATABASE_NAME: 'autojs_control',
    FRONTEND_HOST: '你的服务器公网IP', // 需要替换
    FRONTEND_PORT: 80
  }
}
```

## 🚀 部署流程

### 第一步：准备远程服务器

#### Linux服务器准备
```bash
# 1. 更新系统
sudo apt update && sudo apt upgrade -y

# 2. 安装Node.js 16+
curl -fsSL https://deb.nodesource.com/setup_16.x | sudo -E bash -
sudo apt-get install -y nodejs

# 3. 安装PM2进程管理器
sudo npm install -g pm2

# 4. 安装Nginx
sudo apt install nginx -y

# 5. 安装MySQL (可选，也可使用云数据库)
sudo apt install mysql-server -y

# 6. 配置防火墙
sudo ufw allow 22    # SSH
sudo ufw allow 80    # HTTP
sudo ufw allow 443   # HTTPS
sudo ufw allow 3002  # Node.js服务
sudo ufw allow 3306  # MySQL (如果需要外部访问)
sudo ufw enable
```

#### Windows服务器准备
```powershell
# 1. 下载并安装Node.js 16+ (https://nodejs.org/)
# 2. 安装PM2
npm install -g pm2
npm install -g pm2-windows-startup
pm2-startup install

# 3. 安装MySQL (https://dev.mysql.com/downloads/mysql/)
# 4. 配置Windows防火墙，开放端口：80, 443, 3002, 3306
```

### 第二步：配置远程数据库

#### 创建数据库和用户
```sql
-- 连接到MySQL服务器
mysql -u root -p

-- 创建数据库
CREATE DATABASE autojs_control CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户并授权
CREATE USER 'autojs_control'@'%' IDENTIFIED BY '你的强密码';
GRANT ALL PRIVILEGES ON autojs_control.* TO 'autojs_control'@'%';
FLUSH PRIVILEGES;

-- 退出MySQL
EXIT;
```

#### 导入数据库结构
```bash
# 上传初始化数据库.sql到服务器
scp 初始化数据库.sql user@your-server-ip:/tmp/

# 在服务器上执行
mysql -u autojs_control -p autojs_control < /tmp/初始化数据库.sql
```

### 第三步：修改配置文件

#### 1. 创建统一配置文件
```bash
# 在项目根目录创建config文件夹
mkdir config
```

创建 `config/environment.js`：
```javascript
const env = process.env.NODE_ENV || 'development';

const config = {
  development: {
    SERVER_HOST: 'localhost',
    SERVER_PORT: 3002,
    DATABASE_HOST: 'localhost',
    DATABASE_PORT: 3306,
    DATABASE_USER: 'autojs_control',
    DATABASE_PASSWORD: 'root',
    DATABASE_NAME: 'autojs_control'
  },
  
  production: {
    SERVER_HOST: process.env.SERVER_HOST || '你的服务器公网IP',
    SERVER_PORT: process.env.SERVER_PORT || 3002,
    DATABASE_HOST: process.env.DB_HOST || '你的数据库服务器IP',
    DATABASE_PORT: process.env.DB_PORT || 3306,
    DATABASE_USER: process.env.DB_USER || 'autojs_control',
    DATABASE_PASSWORD: process.env.DB_PASSWORD || '你的数据库密码',
    DATABASE_NAME: process.env.DB_NAME || 'autojs_control'
  }
};

module.exports = config[env];
```

#### 2. 修改数据库配置文件
修改 `server/config/database.js`：
```javascript
const mysql = require('mysql2/promise');
const config = require('../../config/environment');

const dbConfig = {
  host: config.DATABASE_HOST,
  user: config.DATABASE_USER,
  password: config.DATABASE_PASSWORD,
  database: config.DATABASE_NAME,
  charset: 'utf8mb4',
  timezone: '+08:00'
};

// 其余代码保持不变...
```

#### 3. 修改前端配置
修改 `web/vue.config.js`：
```javascript
const config = require('../config/environment');

module.exports = {
  transpileDependencies: true,
  
  devServer: {
    port: 8080,
    host: '0.0.0.0',
    proxy: {
      '/api': {
        target: `http://${config.SERVER_HOST}:${config.SERVER_PORT}`,
        changeOrigin: true,
        ws: true,
        secure: false
      },
      '/socket.io': {
        target: `http://${config.SERVER_HOST}:${config.SERVER_PORT}`,
        changeOrigin: true,
        ws: true,
        secure: false
      }
    }
  },
  
  publicPath: process.env.NODE_ENV === 'production' ? './' : '/',
  // 其余配置...
};
```

#### 4. 修改前端服务器配置
修改 `web/src/utils/serverConfig.js`：
```javascript
export function getServerUrl() {
  const currentHostname = window.location.hostname;
  const currentPort = window.location.port;
  
  // 开发环境检测
  if (process.env.NODE_ENV === 'development' || 
      currentPort === '8080' || currentPort === '8081') {
    return ''; // 使用代理
  }
  
  // 生产环境：使用当前访问的主机名
  const protocol = window.location.protocol === 'https:' ? 'https' : 'http';
  const serverPort = currentPort === '80' || currentPort === '443' ? '3002' : '3002';
  
  return `${protocol}://${currentHostname}:${serverPort}`;
}
```

#### 5. 修改设备端连接配置
修改 `scripts/双向.js`：
```javascript
// 在文件开头添加配置检测
var serverUrl = "http://你的服务器公网IP:3002"; // 生产环境地址

// 或者使用动态检测（推荐）
function getServerUrl() {
    // 可以通过HTTP请求获取配置，或使用固定地址
    return "http://你的服务器公网IP:3002";
}

var serverUrl = getServerUrl();
```

### 第四步：构建和部署前端

#### 构建前端项目
```bash
# 进入前端目录
cd web

# 安装依赖
npm install

# 构建生产版本
npm run build
```

#### 配置Nginx
创建 `/etc/nginx/sites-available/autojs-control`：
```nginx
server {
    listen 80;
    server_name 你的服务器公网IP;
    
    # 前端静态文件
    location / {
        root /var/www/autojs-control;
        index index.html;
        try_files $uri $uri/ /index.html;
    }
    
    # API代理到Node.js
    location /api/ {
        proxy_pass http://localhost:3002;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
    
    # WebSocket代理
    location /socket.io/ {
        proxy_pass http://localhost:3002;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

#### 部署前端文件
```bash
# 创建网站目录
sudo mkdir -p /var/www/autojs-control

# 复制构建文件
sudo cp -r web/dist/* /var/www/autojs-control/

# 设置权限
sudo chown -R www-data:www-data /var/www/autojs-control
sudo chmod -R 755 /var/www/autojs-control

# 启用站点
sudo ln -s /etc/nginx/sites-available/autojs-control /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

### 第五步：部署后端服务

#### 上传后端代码
```bash
# 创建应用目录
sudo mkdir -p /opt/autojs-control
sudo chown $USER:$USER /opt/autojs-control

# 上传代码（使用scp或git）
scp -r server/ user@your-server-ip:/opt/autojs-control/
scp -r jb/ user@your-server-ip:/opt/autojs-control/
scp -r xy-jb/ user@your-server-ip:/opt/autojs-control/
scp -r scripts/ user@your-server-ip:/opt/autojs-control/
scp -r config/ user@your-server-ip:/opt/autojs-control/
scp package.json user@your-server-ip:/opt/autojs-control/
```

#### 安装依赖和启动服务
```bash
# 进入应用目录
cd /opt/autojs-control

# 安装依赖
npm install --production

# 设置环境变量
export NODE_ENV=production
export SERVER_HOST=你的服务器公网IP
export DB_HOST=你的数据库服务器IP
export DB_PASSWORD=你的数据库密码

# 创建PM2配置文件
cat > ecosystem.config.js << EOF
module.exports = {
  apps: [{
    name: 'autojs-control',
    script: 'server/server-main.js',
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'development'
    },
    env_production: {
      NODE_ENV: 'production',
      SERVER_HOST: '你的服务器公网IP',
      DB_HOST: '你的数据库服务器IP',
      DB_PASSWORD: '你的数据库密码'
    }
  }]
};
EOF

# 启动服务
pm2 start ecosystem.config.js --env production

# 保存PM2配置
pm2 save
pm2 startup
```

### 第六步：配置SSL证书（可选但推荐）

#### 使用Let's Encrypt免费证书
```bash
# 安装certbot
sudo apt install certbot python3-certbot-nginx -y

# 获取证书
sudo certbot --nginx -d 你的域名

# 自动续期
sudo crontab -e
# 添加以下行：
# 0 12 * * * /usr/bin/certbot renew --quiet
```

### 第七步：验证部署

#### 检查服务状态
```bash
# 检查PM2状态
pm2 status

# 检查Nginx状态
sudo systemctl status nginx

# 检查MySQL连接
mysql -h 你的数据库服务器IP -u autojs_control -p

# 检查端口监听
netstat -tlnp | grep :3002
netstat -tlnp | grep :80
```

#### 测试功能
1. **访问前端**: `http://你的服务器公网IP`
2. **测试API**: `http://你的服务器公网IP:3002/api/devices`
3. **测试WebSocket**: 在前端查看设备连接状态
4. **测试设备连接**: 运行修改后的`scripts/双向.js`

## 🔒 安全配置

### 防火墙配置
```bash
# 只开放必要端口
sudo ufw default deny incoming
sudo ufw default allow outgoing
sudo ufw allow ssh
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw allow 3002/tcp
sudo ufw enable
```

### 数据库安全
```bash
# 运行MySQL安全脚本
sudo mysql_secure_installation

# 限制数据库访问IP
# 在MySQL配置中添加bind-address限制
```

### 应用安全
- 使用强密码
- 定期更新系统和依赖
- 配置日志监控
- 设置备份策略

## 📊 监控和维护

### 日志查看
```bash
# PM2日志
pm2 logs autojs-control

# Nginx日志
sudo tail -f /var/log/nginx/access.log
sudo tail -f /var/log/nginx/error.log

# 系统日志
sudo journalctl -u nginx -f
```

### 性能监控
```bash
# PM2监控
pm2 monit

# 系统资源
htop
df -h
free -h
```

## 🚨 故障排除

### 常见问题
1. **端口被占用**: `sudo lsof -i :3002`
2. **权限问题**: 检查文件权限和用户组
3. **数据库连接失败**: 检查防火墙和用户权限
4. **WebSocket连接失败**: 检查Nginx代理配置
5. **设备连接失败**: 检查服务器IP配置和防火墙

### 回滚方案
```bash
# 停止服务
pm2 stop autojs-control

# 恢复备份
# 重新部署旧版本

# 重启服务
pm2 start autojs-control
```

## 📝 部署检查清单

- [ ] 服务器环境准备完成
- [ ] 数据库创建和配置完成
- [ ] 配置文件修改完成
- [ ] 前端构建和部署完成
- [ ] 后端部署和启动完成
- [ ] Nginx配置完成
- [ ] SSL证书配置完成（可选）
- [ ] 防火墙配置完成
- [ ] 功能测试通过
- [ ] 监控配置完成
- [ ] 备份策略制定完成

完成以上步骤后，你的Auto.js云群控系统就成功部署到远程服务器了！
