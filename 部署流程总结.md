# Auto.js云群控系统部署流程总结

## 📋 部署资源清单

### 📄 核心文档
- **远程服务器部署流程文档.md** - 详细的部署步骤指南
- **快速部署指南.md** - 简化的一键部署流程
- **宝塔面板部署流程.md** - 宝塔面板专用部署指南 ⭐
- **前端系统文件功能明细.md** - 前端架构分析
- **服务器系统文件功能明细.md** - 后端架构分析
- **系统架构总览.md** - 整体系统架构

### 🔧 自动化工具
- **deploy-config.js** - 一键配置修改脚本（通用版）
- **deploy-config-bt.js** - 宝塔面板专用配置脚本 ⭐
- **deploy-check.sh** - 部署环境检查脚本

## 🚀 部署方案选择

### 方案一：宝塔面板部署（推荐） ⭐

#### 第一步：本地准备（在你的开发机上）
```bash
# 运行宝塔专用配置脚本
node deploy-config-bt.js
```

#### 第二步：宝塔面板环境准备
1. **安装宝塔面板**（Linux/Windows）
2. **安装必要软件**：Nginx, MySQL, PM2管理器, Node.js
3. **创建网站和数据库**
4. **配置安全组和防火墙**

#### 第三步：上传和部署
1. **上传项目到宝塔网站目录**
2. **导入数据库结构**
3. **运行宝塔部署脚本**
4. **在PM2管理器中添加项目**
5. **配置Nginx反向代理**

### 方案二：传统服务器部署

#### 第一步：本地准备（在你的开发机上）
```bash
# 运行通用配置脚本
node deploy-config.js
```

#### 配置信息对比

| 配置项 | 宝塔面板部署 | 传统服务器部署 |
|-------|-------------|---------------|
| 服务器地址 | 公网IP或域名 | 公网IP地址 |
| 数据库主机 | localhost（宝塔默认） | 数据库服务器IP |
| 网站目录 | /www/wwwroot/sitename | 自定义路径 |
| 服务管理 | 宝塔PM2管理器 | 命令行PM2 |
| Web服务器 | 宝塔Nginx面板 | 手动配置Nginx |
| SSL证书 | 宝塔一键申请 | 手动配置 |

#### 脚本自动完成的操作
✅ 修改所有配置文件中的IP地址
✅ 生成PM2进程管理配置
✅ 生成Nginx配置文件
✅ 生成自动部署脚本
✅ 创建统一环境配置文件
✅ 生成宝塔专用配置（宝塔方案）

#### 第二步：上传到远程服务器
```bash
# 传统方式：SCP上传
scp -r . user@your-server-ip:/opt/autojs-control/

# 或使用Git克隆
git clone your-repo-url /opt/autojs-control
```

#### 第三步：在远程服务器上部署
```bash
# SSH连接服务器
ssh user@your-server-ip
cd /opt/autojs-control

# 运行部署脚本
chmod +x deploy.sh && ./deploy.sh

# 配置Nginx和检查状态
./deploy-check.sh
```

## 🎯 宝塔面板部署优势

### 🌟 操作简便性
- **可视化界面**：无需命令行操作，图形化管理
- **一键安装**：软件商店一键安装所需环境
- **配置简单**：面板化配置，降低出错率

### 🔧 功能完整性
- **集成PM2**：内置PM2管理器，进程管理更方便
- **SSL证书**：一键申请Let's Encrypt免费证书
- **监控告警**：内置系统监控和告警功能
- **备份恢复**：定时备份，一键恢复

### 🛡️ 安全可靠性
- **防火墙管理**：可视化防火墙配置
- **访问控制**：IP白名单，访问限制
- **安全扫描**：定期安全检查和漏洞扫描
- **日志管理**：完整的访问和错误日志

### 📊 维护便利性
- **性能监控**：实时系统资源监控
- **日志查看**：集中化日志管理
- **版本管理**：软件版本升级管理
- **故障诊断**：内置故障诊断工具

## 🎯 关键配置文件自动修改

### 自动修改的配置文件
| 文件路径 | 修改内容 | 作用 |
|---------|---------|------|
| `server/config/database.js` | 数据库连接配置 | 后端连接数据库 |
| `web/vue.config.js` | 前端代理配置 | 开发环境API代理 |
| `scripts/双向.js` | 设备端连接配置 | 设备连接服务器 |
| `web/src/utils/serverConfig.js` | 前端服务器配置 | 生产环境API地址 |

### 自动生成的配置文件
| 文件名 | 作用 | 说明 |
|-------|------|------|
| `ecosystem.config.js` | PM2进程管理配置 | 生产环境服务启动 |
| `nginx-autojs-control.conf` | Nginx配置文件 | Web服务器配置 |
| `deploy.sh` | 自动部署脚本 | 一键部署执行 |
| `config/environment.js` | 统一环境配置 | 环境变量管理 |

## 📊 IP地址统一管理

### 需要配置的地址
- **前端API请求地址**：`http://你的服务器IP:3002`
- **WebSocket连接地址**：`ws://你的服务器IP:3002`
- **设备端连接地址**：`http://你的服务器IP:3002`
- **数据库连接地址**：`你的数据库服务器IP:3306`

### 统一配置的优势
✅ 一次配置，全局生效  
✅ 避免遗漏修改某个文件  
✅ 减少人为错误  
✅ 支持环境变量覆盖  

## 🔄 部署后的访问方式

### Web管理界面
```
http://你的服务器IP
```
- 用户登录页面
- 设备管理界面
- 脚本配置页面
- 执行日志查看

### API接口
```
http://你的服务器IP:3002/api/
```
- 设备管理API
- 脚本执行API
- 文件上传API
- 状态查询API

### 设备连接
```
http://你的服务器IP:3002
```
- 设备端脚本自动连接
- WebSocket实时通信
- 状态上报接口
- 命令接收接口

## 🛠️ 支持的服务器环境

### Linux服务器（推荐）
- **操作系统**：Ubuntu 18.04+, CentOS 7+, Debian 9+
- **服务管理**：systemd, PM2
- **Web服务器**：Nginx
- **数据库**：MySQL 5.7+, MariaDB 10.3+

### Windows服务器
- **操作系统**：Windows Server 2016+, Windows 10+
- **服务管理**：PM2, Windows服务
- **Web服务器**：IIS, Nginx for Windows
- **数据库**：MySQL 5.7+, SQL Server

## 🔍 部署验证检查

### 自动检查项目
运行 `./deploy-check.sh` 会检查：

#### 系统环境
- ✅ 操作系统类型
- ✅ Node.js版本（>=16）
- ✅ PM2安装状态
- ✅ Nginx安装状态（Linux）

#### 项目文件
- ✅ 核心文件存在性
- ✅ 配置文件完整性
- ✅ 脚本文件可执行性

#### 服务状态
- ✅ 端口监听状态（3002, 80, 3306）
- ✅ PM2进程运行状态
- ✅ 系统服务状态

#### 资源状态
- ✅ 磁盘空间充足性
- ✅ 内存使用情况
- ✅ 网络连接状态

### 手动验证步骤
1. **访问前端页面**：`http://你的服务器IP`
2. **测试用户登录**：使用默认管理员账户
3. **检查设备连接**：运行设备端脚本测试连接
4. **测试API接口**：`http://你的服务器IP:3002/api/devices`
5. **验证WebSocket**：查看实时状态更新

## 🚨 常见问题快速解决

### 端口被占用
```bash
# 查看端口占用
sudo lsof -i :3002
# 杀死占用进程
sudo kill -9 PID
```

### 数据库连接失败
```bash
# 检查MySQL服务
sudo systemctl status mysql
# 测试连接
mysql -h 你的数据库IP -u autojs_control -p
```

### PM2服务启动失败
```bash
# 查看详细日志
pm2 logs autojs-control --lines 50
# 重启服务
pm2 restart autojs-control
```

### Nginx配置错误
```bash
# 测试配置
sudo nginx -t
# 查看错误日志
sudo tail -f /var/log/nginx/error.log
```

## 📈 部署后优化建议

### 性能优化
- 配置数据库索引
- 启用Nginx gzip压缩
- 设置PM2集群模式
- 配置Redis缓存（可选）

### 安全优化
- 配置SSL证书
- 设置防火墙规则
- 限制数据库访问IP
- 定期更新系统补丁

### 监控配置
- 设置日志轮转
- 配置性能监控
- 设置告警通知
- 制定备份策略

## 🎉 部署成功标志

当以下条件都满足时，表示部署成功：

✅ `./deploy-check.sh` 检查全部通过  
✅ 可以正常访问Web管理界面  
✅ 设备端脚本可以成功连接服务器  
✅ API接口响应正常  
✅ WebSocket实时通信正常  
✅ 数据库读写操作正常  

## 📞 技术支持

如果在部署过程中遇到问题：

1. **首先运行**：`./deploy-check.sh` 检查系统状态
2. **查看日志**：`pm2 logs autojs-control`
3. **检查配置**：确认IP地址和端口配置正确
4. **网络测试**：确认防火墙和网络连接正常
5. **参考文档**：查看详细的部署流程文档

---

**🎯 一键部署，轻松上云！**
