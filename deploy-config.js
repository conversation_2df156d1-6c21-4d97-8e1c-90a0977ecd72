#!/usr/bin/env node

/**
 * Auto.js云群控系统部署配置脚本
 * 用于快速修改所有配置文件中的服务器地址
 */

const fs = require('fs');
const path = require('path');
const readline = require('readline');

// 创建命令行接口
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// 配置文件路径
const CONFIG_FILES = {
  database: 'server/config/database.js',
  vueConfig: 'web/vue.config.js',
  serverConfig: 'web/src/utils/serverConfig.js',
  deviceScript: 'scripts/双向.js',
  serverCore: 'server/core/server-core.js'
};

// 提示用户输入配置信息
function promptConfig() {
  return new Promise((resolve) => {
    const config = {};
    
    console.log('\n🚀 Auto.js云群控系统部署配置工具');
    console.log('='.repeat(50));
    
    rl.question('请输入服务器公网IP地址: ', (serverIP) => {
      config.serverIP = serverIP;
      
      rl.question('请输入数据库服务器IP地址 (回车使用服务器IP): ', (dbIP) => {
        config.dbIP = dbIP || serverIP;
        
        rl.question('请输入数据库用户名 (默认: autojs_control): ', (dbUser) => {
          config.dbUser = dbUser || 'autojs_control';
          
          rl.question('请输入数据库密码: ', (dbPassword) => {
            config.dbPassword = dbPassword;
            
            rl.question('请输入数据库名称 (默认: autojs_control): ', (dbName) => {
              config.dbName = dbName || 'autojs_control';
              
              rl.question('请输入服务器端口 (默认: 3002): ', (serverPort) => {
                config.serverPort = serverPort || '3002';
                
                rl.close();
                resolve(config);
              });
            });
          });
        });
      });
    });
  });
}

// 备份原文件
function backupFile(filePath) {
  if (fs.existsSync(filePath)) {
    const backupPath = filePath + '.backup.' + Date.now();
    fs.copyFileSync(filePath, backupPath);
    console.log(`✅ 已备份: ${filePath} -> ${backupPath}`);
  }
}

// 修改数据库配置文件
function updateDatabaseConfig(config) {
  const filePath = CONFIG_FILES.database;
  if (!fs.existsSync(filePath)) {
    console.log(`❌ 文件不存在: ${filePath}`);
    return;
  }
  
  backupFile(filePath);
  
  let content = fs.readFileSync(filePath, 'utf8');
  
  // 替换数据库配置
  content = content.replace(
    /host:\s*['"`].*?['"`]/,
    `host: '${config.dbIP}'`
  );
  content = content.replace(
    /user:\s*['"`].*?['"`]/,
    `user: '${config.dbUser}'`
  );
  content = content.replace(
    /password:\s*['"`].*?['"`]/,
    `password: '${config.dbPassword}'`
  );
  content = content.replace(
    /database:\s*['"`].*?['"`]/,
    `database: '${config.dbName}'`
  );
  
  fs.writeFileSync(filePath, content);
  console.log(`✅ 已更新: ${filePath}`);
}

// 修改Vue配置文件
function updateVueConfig(config) {
  const filePath = CONFIG_FILES.vueConfig;
  if (!fs.existsSync(filePath)) {
    console.log(`❌ 文件不存在: ${filePath}`);
    return;
  }
  
  backupFile(filePath);
  
  let content = fs.readFileSync(filePath, 'utf8');
  
  // 替换代理目标地址
  const serverUrl = `http://${config.serverIP}:${config.serverPort}`;
  content = content.replace(
    /target:\s*['"`]http:\/\/.*?['"`]/g,
    `target: '${serverUrl}'`
  );
  
  // 替换日志中的地址
  content = content.replace(
    /http:\/\/[\d\.]+:\d+/g,
    serverUrl
  );
  content = content.replace(
    /ws:\/\/[\d\.]+:\d+/g,
    `ws://${config.serverIP}:${config.serverPort}`
  );
  
  fs.writeFileSync(filePath, content);
  console.log(`✅ 已更新: ${filePath}`);
}

// 修改前端服务器配置文件
function updateServerConfig(config) {
  const filePath = CONFIG_FILES.serverConfig;
  if (!fs.existsSync(filePath)) {
    console.log(`❌ 文件不存在: ${filePath}`);
    return;
  }
  
  backupFile(filePath);
  
  let content = fs.readFileSync(filePath, 'utf8');
  
  // 如果文件存在getServerUrl函数，更新其中的IP地址
  if (content.includes('getServerUrl')) {
    content = content.replace(
      /http:\/\/[\d\.]+:\d+/g,
      `http://${config.serverIP}:${config.serverPort}`
    );
    content = content.replace(
      /https:\/\/[\d\.]+:\d+/g,
      `https://${config.serverIP}:${config.serverPort}`
    );
  }
  
  fs.writeFileSync(filePath, content);
  console.log(`✅ 已更新: ${filePath}`);
}

// 修改设备端脚本配置
function updateDeviceScript(config) {
  const filePath = CONFIG_FILES.deviceScript;
  if (!fs.existsSync(filePath)) {
    console.log(`❌ 文件不存在: ${filePath}`);
    return;
  }
  
  backupFile(filePath);
  
  let content = fs.readFileSync(filePath, 'utf8');
  
  // 替换服务器URL
  const serverUrl = `http://${config.serverIP}:${config.serverPort}`;
  content = content.replace(
    /var serverUrl = ["']http:\/\/.*?["'];/,
    `var serverUrl = "${serverUrl}";`
  );
  
  fs.writeFileSync(filePath, content);
  console.log(`✅ 已更新: ${filePath}`);
}

// 修改服务器核心配置
function updateServerCore(config) {
  const filePath = CONFIG_FILES.serverCore;
  if (!fs.existsSync(filePath)) {
    console.log(`❌ 文件不存在: ${filePath}`);
    return;
  }
  
  backupFile(filePath);
  
  let content = fs.readFileSync(filePath, 'utf8');
  
  // 替换端口配置
  content = content.replace(
    /const PORT = \d+;/,
    `const PORT = ${config.serverPort};`
  );
  content = content.replace(
    /port:\s*\d+/,
    `port: ${config.serverPort}`
  );
  
  fs.writeFileSync(filePath, content);
  console.log(`✅ 已更新: ${filePath}`);
}

// 创建环境配置文件
function createEnvironmentConfig(config) {
  const configDir = 'config';
  const filePath = path.join(configDir, 'environment.js');
  
  // 创建config目录
  if (!fs.existsSync(configDir)) {
    fs.mkdirSync(configDir, { recursive: true });
    console.log(`✅ 已创建目录: ${configDir}`);
  }
  
  const envConfig = `const env = process.env.NODE_ENV || 'development';

const config = {
  development: {
    SERVER_HOST: 'localhost',
    SERVER_PORT: 3002,
    DATABASE_HOST: 'localhost',
    DATABASE_PORT: 3306,
    DATABASE_USER: 'autojs_control',
    DATABASE_PASSWORD: 'root',
    DATABASE_NAME: 'autojs_control'
  },
  
  production: {
    SERVER_HOST: process.env.SERVER_HOST || '${config.serverIP}',
    SERVER_PORT: process.env.SERVER_PORT || ${config.serverPort},
    DATABASE_HOST: process.env.DB_HOST || '${config.dbIP}',
    DATABASE_PORT: process.env.DB_PORT || 3306,
    DATABASE_USER: process.env.DB_USER || '${config.dbUser}',
    DATABASE_PASSWORD: process.env.DB_PASSWORD || '${config.dbPassword}',
    DATABASE_NAME: process.env.DB_NAME || '${config.dbName}'
  }
};

module.exports = config[env];
`;
  
  fs.writeFileSync(filePath, envConfig);
  console.log(`✅ 已创建: ${filePath}`);
}

// 创建PM2配置文件
function createPM2Config(config) {
  const pm2Config = `module.exports = {
  apps: [{
    name: 'autojs-control',
    script: 'server-main.js',
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'development'
    },
    env_production: {
      NODE_ENV: 'production',
      SERVER_HOST: '${config.serverIP}',
      SERVER_PORT: ${config.serverPort},
      DB_HOST: '${config.dbIP}',
      DB_USER: '${config.dbUser}',
      DB_PASSWORD: '${config.dbPassword}',
      DB_NAME: '${config.dbName}'
    },
    error_file: './logs/err.log',
    out_file: './logs/out.log',
    log_file: './logs/combined.log',
    time: true
  }]
};
`;
  
  fs.writeFileSync('ecosystem.config.js', pm2Config);
  console.log(`✅ 已创建: ecosystem.config.js`);
}

// 创建Nginx配置文件
function createNginxConfig(config) {
  const nginxConfig = `server {
    listen 80;
    server_name ${config.serverIP};
    
    # 前端静态文件
    location / {
        root /var/www/autojs-control;
        index index.html;
        try_files $uri $uri/ /index.html;
    }
    
    # API代理到Node.js
    location /api/ {
        proxy_pass http://localhost:${config.serverPort};
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
    
    # WebSocket代理
    location /socket.io/ {
        proxy_pass http://localhost:${config.serverPort};
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
`;
  
  fs.writeFileSync('nginx-autojs-control.conf', nginxConfig);
  console.log(`✅ 已创建: nginx-autojs-control.conf`);
}

// 生成部署脚本
function createDeployScript(config) {
  const deployScript = `#!/bin/bash

# Auto.js云群控系统部署脚本
echo "🚀 开始部署Auto.js云群控系统..."

# 设置环境变量
export NODE_ENV=production
export SERVER_HOST=${config.serverIP}
export SERVER_PORT=${config.serverPort}
export DB_HOST=${config.dbIP}
export DB_USER=${config.dbUser}
export DB_PASSWORD=${config.dbPassword}
export DB_NAME=${config.dbName}

# 安装依赖
echo "📦 安装Node.js依赖..."
npm install --production

# 构建前端
echo "🏗️ 构建前端项目..."
cd web
npm install
npm run build
cd ..

# 创建日志目录
mkdir -p logs

# 启动服务
echo "🚀 启动服务..."
pm2 start ecosystem.config.js --env production

# 保存PM2配置
pm2 save

echo "✅ 部署完成！"
echo "📊 查看服务状态: pm2 status"
echo "📝 查看日志: pm2 logs autojs-control"
echo "🌐 访问地址: http://${config.serverIP}"
`;
  
  fs.writeFileSync('deploy.sh', deployScript);
  fs.chmodSync('deploy.sh', '755');
  console.log(`✅ 已创建: deploy.sh`);
}

// 主函数
async function main() {
  try {
    const config = await promptConfig();
    
    console.log('\n📝 配置信息:');
    console.log(`服务器IP: ${config.serverIP}`);
    console.log(`数据库IP: ${config.dbIP}`);
    console.log(`数据库用户: ${config.dbUser}`);
    console.log(`数据库名称: ${config.dbName}`);
    console.log(`服务器端口: ${config.serverPort}`);
    
    console.log('\n🔧 开始更新配置文件...');
    
    // 更新所有配置文件
    updateDatabaseConfig(config);
    updateVueConfig(config);
    updateServerConfig(config);
    updateDeviceScript(config);
    updateServerCore(config);
    
    // 创建新的配置文件
    createEnvironmentConfig(config);
    createPM2Config(config);
    createNginxConfig(config);
    createDeployScript(config);
    
    console.log('\n🎉 配置更新完成！');
    console.log('\n📋 接下来的步骤:');
    console.log('1. 将项目文件上传到服务器');
    console.log('2. 在服务器上运行: chmod +x deploy.sh && ./deploy.sh');
    console.log('3. 配置Nginx: sudo cp nginx-autojs-control.conf /etc/nginx/sites-available/');
    console.log('4. 启用站点: sudo ln -s /etc/nginx/sites-available/nginx-autojs-control.conf /etc/nginx/sites-enabled/');
    console.log('5. 重启Nginx: sudo systemctl reload nginx');
    console.log(`6. 访问系统: http://${config.serverIP}`);
    
  } catch (error) {
    console.error('❌ 配置过程中出现错误:', error.message);
    process.exit(1);
  }
}

// 运行主函数
main();
